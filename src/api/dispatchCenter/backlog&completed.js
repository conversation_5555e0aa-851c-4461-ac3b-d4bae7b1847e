import { getRequest, postRequestBody3, getRequestByValue, putRequest, postRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dispatchTodoList: `${baseURL}workflow/dispatch/todoList`,
    dispatchFinishedList: `${baseURL}workflow/dispatch/finishedList`,
    statistics: `${baseURL}workflow/workStation/statistics`,
    category: `${baseURL}workflow/category/list`,
    todoExport: `${baseURL}workflow/dispatch/todoList/export`,
    finishedExport: `${baseURL}workflow/dispatch/finishedList/export`,
    preTotalExport: `${baseURL}workflow/dispatch/presale/allProcessStatus/export`,
    midTotalExport: `${baseURL}workflow/dispatch/midsale/allProcessStatus/export`,
    getOwnerUserList: `${baseURL}workflow/workStation/getOwnerUserList`,
    urge: `${baseURL}workflow/dispatch/urge/`,
}

// 获取售前多模块待办流程表格数据
export const getDispatchTodoList = (data) => getRequest(url.dispatchTodoList, data)

// 获取售前多模块已办流程表格数据
export const getDispatchCompletedData = (data) => getRequest(url.dispatchFinishedList, data)

// 获取统计数据接口
export const getStatisticsData = (data) => getRequest(url.statistics, data)

// 获取流程分类接口
export const getClassificationData = (data) => getRequest(url.category, data)

// 调度审批待办导出
export const processDispatchTodoListExport = (params) => getRequest(url.todoExport, params)

// 调度审批已办导出
export const processDispatchFinishedListExport = (params) => getRequest(url.finishedExport, params)

// 售前待办已办全部导出
export const processPreTotalListExport = (params) => getRequest(url.preTotalExport, params)

//售中待办已办全部导出
export const processMidTotalListExport = (params) => getRequest(url.midTotalExport, params)

// 获取自有能力方列表
export const getOwnerUserList = (params) => getRequest(url.getOwnerUserList, params)

// 催办
export const remindWorkOrder = (data) => getRequestByValue(url.urge, data);