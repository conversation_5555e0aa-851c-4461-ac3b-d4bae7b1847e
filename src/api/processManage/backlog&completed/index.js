import request from "@/utils/processRequest.js";
import { isDev } from "@/setting.js";

const UrlPrefix = isDev ? "/portal" : "";

// 地市驳回
export function cityBack(params) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/rejectVTwo",
    method: "post",
    data: params
  });
};
// 批量审批接口
export function batchSubmitWithdrawProcess(params) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/complete",
    method: "post",
    data: params
  });
};
// 售前工单转售中
export function midDispatch(params) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/midDispatch",
    method: "post",
    data: params
  });
};
