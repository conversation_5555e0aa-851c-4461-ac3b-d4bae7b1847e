import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/dispatchCenter',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "调度中心",
        },
        children: [
            {
                path: 'workbench',
                name: 'workbench',
                meta: {
                    title: "调度中心"
                },
                component: () => import('@/views/dispatchCenter/workbench/index.vue')
            },
            {
                path: 'starWork',
                name: 'starWork',
                meta: {
                    title: "工单信息"
                },
                component: () =>
                    import('@/views/dispatchCenter/starWork/index.vue')

            },
            {
                path: 'coordination',
                name: 'coordination',
                meta: {
                    title: "售中调度"
                },
                component: () => import('@/views/dispatchCenter/coordination/index.vue')
            },
        ]
    },
]

export default ROUTES;