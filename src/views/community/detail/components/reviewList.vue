<template>
  <div class="textWord">我要留言</div>
  <div class="area">
    <div class="area_tit">
      <a-textarea
        v-model:value="content"
        placeholder="请输入你想评论的内容，内容不超过100字"
        :rows="4"
        showCount
        :maxlength="100"
      />
      <div class="flex just-end align-center margin_t_16">
        <a-checkbox v-model:checked="checked">匿名</a-checkbox>
        <a-button
          type="primary"
          style="
            background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
            border-radius: 4px;
            font-weight: 500;
            border: none;
          "
          @click="toComent"
        >
          发布
        </a-button>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, computed } from "vue";
import { setComment } from "@/api/community/index.js";
import { message } from "ant-design-vue";
import eventBus from "@/utils/eventBus";
export default defineComponent({
  name: "reviewList",
  props: {
    id: {
      type: String,
      default: null,
    },
  },
  emits: ["refresh-page"],
  setup(props, { emit }) {
    const data = reactive({
      content: "",
      checked: false,
      id: computed(() => props.id || ""),
    });
    const toComent = () => {
      // Trim and validate content
      const content = data.content;
      if (!content) {
        message.warning("留言内容不能为空");
        return;
      }
      const params = {
        postId: data.id,
        content: content,
        anonymity: data.checked ? 1 : 0,
      };
      setComment(params)
        .then((res) => {
          if (res.code === 200) {
            message.success("留言成功");
            emit("refresh-page");
            eventBus.emit("communityTable");
            data.content = "";
            data.checked = false;
          } else {
            message.error(res.message || "留言失败");
          }
        })
        .catch((error) => {
          console.error("留言出错:", error);
          message.error("留言提交出错，请重试");
        });
    };
    return {
      ...toRefs(data),
      toComent,
    };
  },
});
</script>
<style lang="scss" scoped>
.textWord {
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.area {
  margin-top: 16px;
  .area_tit {
    text-align: left;

    .area_title {
      font-weight: 500;
      font-size: 14px;
      color: #2e3852;
      line-height: 28px;
    }
  }

  textarea {
    border: none;
    background-color: #f5f7fc;
  }
}
</style>