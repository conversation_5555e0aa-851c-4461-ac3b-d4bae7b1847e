<template>
  <a-modal :visible="visible" title="生态厂商新增" :footer="null" @cancel="handleClose" width="50%">
    <a-form ref="formRef" :model="formData" labelAlign="right" :rules="rules" :colon="false">
      <a-row>
        <a-col :span="24">
          <a-form-item label="生态合作方" name="company">
            <a-select placeholder="请选择生态合作方" v-model:value="formData.company" allowClear show-search
              @change="handleEcologyChange" @clear="handleEcologyChange"
              :not-found-content="fetching ? undefined : null" :virtual-scroll="{
                itemHeight: 32,
                height: 400,
                remain: 8,
              }">
              <template #notFoundContent>
                <div style="text-align: center;">
                  <a-spin size="small" />
                  <span style="margin-left: 8px">加载中...</span>
                </div>
              </template>
              <template v-for="opt in displayOptions" :key="opt.name">
                <a-select-option :value="opt.name" :disabled="disabledCompanyIds.includes(opt.enterpriseId)">
                  {{ opt.name }}
                </a-select-option>
              </template>
            </a-select>
            <div v-if="formData.company">
              <p v-if="formData.sync != 1 || formData.auth != 1"
                style="color: red; margin-bottom: 0; margin-left: 12px">
                该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
              </p>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="生态联系人" name="contact">
            <a-select placeholder="请选择生态联系人" v-model:value="formData.contact" allowClear @change="handleContactChange"
              @clear="handleContactChange">
              <template v-for="(opt, index) in contactList" :key="index">
                <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                  {{ opt.contactName }}
                </a-select-option>
              </template>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="生态联系方式">
            <a-input disabled :value="formData.phone" placeholder="请输入生态联系方式" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="负责区域">
            <a-input disabled :value="formData.area" placeholder="请选择负责区域" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="modal-footer">
      <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleClose">取消</a-button>
      <a-button class="custom_btn active_btn" @click="handleSubmit" :disabled="isSyncAuth">提交</a-button>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch, computed } from 'vue';

export default defineComponent({
  name: 'EcoPartnerAddModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    displayOptions: {
      type: Array,
      default: () => []
    },
    contactList: {
      type: Array,
      default: () => []
    },
    disabledCompanyIds: {
      type: Array,
      default: () => []
    },
    fetching: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'ecology-change', 'submit'],
  setup(props, { emit }) {
    const formRef = ref();
    const formData = ref({
      company: undefined,
      contact: undefined,
      phone: undefined,
      area: undefined,
      sync: undefined,
      auth: undefined,
      enterpriseId: undefined,
      userId: undefined,
      approve: undefined,
      totalScore: undefined,
      introScore: undefined,
    });

    const rules = {
      company: [
        { required: true, message: '请选择生态合作方', trigger: 'change' }
      ],
      contact: [
        { required: true, message: '请选择生态联系人', trigger: 'change' }
      ]
    };

    const isSyncAuth = computed(() => {
      return formData.value.sync != 1 || formData.value.auth != 1;
    });

    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        // 重置表单数据
        formData.value = {
          company: undefined,
          contact: undefined,
          phone: undefined,
          area: undefined,
          sync: undefined,
          auth: undefined,
          enterpriseId: undefined,
          userId: undefined,
          approve: undefined,
          totalScore: undefined,
          introScore: undefined,
        };
      }
    });

    const handleEcologyChange = (value) => {
      // 更新本地数据
      const selectedOption = props.displayOptions.find(opt => opt.name === value);
      if (selectedOption) {
        formData.value.totalScore = selectedOption.totalScore;
        formData.value.introScore = selectedOption.introScore;
        formData.value.sync = selectedOption.sync;
        formData.value.auth = selectedOption.auth;
        formData.value.area = selectedOption.address || "江苏省";
      } else {
        formData.value.totalScore = undefined;
        formData.value.introScore = undefined;
        formData.value.sync = undefined;
        formData.value.auth = undefined;
        formData.value.area = undefined;
        formData.value.contact = undefined;
      }
      emit('ecology-change', value);
    };

    const handleContactChange = (value) => {
      // 更新本地数据
      const selectedContact = props.contactList.find(contact => contact.contactName === value);
      if (selectedContact) {
        formData.value.phone = selectedContact.contactPhone;
        formData.value.area = selectedContact.contactAddress || formData.value.area;
        formData.value.enterpriseId = selectedContact.enterpriseId;
        formData.value.userId = selectedContact.userId;
        formData.value.approve = selectedContact.approve;
      } else {
        formData.value.phone = undefined;
        formData.value.area = undefined;
        formData.value.enterpriseId = undefined;
        formData.value.userId = undefined;
        formData.value.approve = undefined;
      }
    };

    const handleClose = () => {
      emit('update:visible', false);
    };

    const handleSubmit = async () => {
      try {
        await formRef.value?.validate();
        emit('submit', formData.value);
      } catch (error) {
        console.log('Validation failed:', error);
      }
    };

    return {
      formRef,
      formData,
      rules,
      isSyncAuth,
      handleEcologyChange,
      handleContactChange,
      handleClose,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/button.scss";

.modal-footer {
  justify-content: center;
  display: flex;
  margin-top: 32px;
}

:deep(.ant-form-item-label) {
  width: 122px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}
</style>
