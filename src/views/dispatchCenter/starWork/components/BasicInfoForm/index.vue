<template>
  <div class="basic-info-form">
    <SectionTitle title="基础信息" />
    <template v-if="isEditMode">
      <div class="base_info">
        <div>
          <a-row>
            <a-col :span="12">
              <a-form-item label="信息来源" name="projectCodeType">
                <a-radio-group v-model:value="localFormData.projectCodeType" @change="handleProjectCodeTypeChange">
                  <a-radio :value="1">项目信息</a-radio>
                  <a-radio :value="2">商机信息</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item :label="localFormData.projectCodeType === 1 ? '项目名称' : '商机名称'" name="projectName">
                <a-select v-if="localFormData.projectCodeType === 1" placeholder="请选择DICT项目管理系统项目名称"
                  v-model:value="localFormData.projectName" allowClear style="width:400px;" show-search
                  :not-found-content="projectNameFetching ? undefined : null" :virtual-scroll="{
                    itemHeight: 32,
                    height: 400,
                    remain: 8,
                  }" :getPopupContainer="getPopupContainer()" @change="handleProjectNameChange">
                  <template #notFoundContent>
                    <div style="text-align: center;">
                      <a-spin size="small" />
                      <span style="margin-left: 8px">加载中...</span>
                    </div>
                  </template>
                  <a-select-option v-for="item in projectOptions" :key="item.name" :value="item.name">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-input v-else v-model:value="localFormData.projectName" placeholder="请输入DICT项目管理系统商机名称"
                  :maxlength="50" style="width:400px;"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="localFormData.projectCodeType === 1 ? '项目编码' : '商机编码'" name="projectCode">
                <a-select v-if="localFormData.projectCodeType === 1" placeholder="请选择DICT项目管理系统项目编码"
                  v-model:value="localFormData.projectCode" allowClear style="width:400px;" show-search
                  :not-found-content="projectCodeFetching ? undefined : null" :virtual-scroll="{
                    itemHeight: 32,
                    height: 400,
                    remain: 8,
                  }" :getPopupContainer="getPopupContainer()" @change="handleProjectCodeChange">
                  <a-select-option v-for="item in projectOptions" :key="item.code" :value="item.code">
                    {{ item.code }}
                  </a-select-option>
                </a-select>
                <a-input v-else v-model:value="localFormData.projectCode" style="width: 400px" type="text"
                  placeholder="请输入DICT项目管理系统商机编码" @input="handleLimitLength" allow-clear>
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-row>
          <a-col :span="12">
            <a-form-item label="需求发起人" name="userInfo">
              <a-input v-model:value="localFormData.userInfo" style="width:400px;"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系方式" name="userPhone">
              <a-input disabled v-model:value="localFormData.userPhone" style="width:400px;"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="支撑方式" name="supportMehod">
              <a-radio-group v-model:value="localFormData.supportMehod">
                <a-radio value="1">远程支撑</a-radio>
                <a-radio value="2">现场支撑</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-config-provider :locale="zhCN">
              <a-form-item label="支撑时限" name="time">
                <a-date-picker style="width: 400px" v-model:value="localFormData.time" format="YYYY-MM-DD"
                  :value-format="'YYYY-MM-DD'" :disabled-date="disabledDate"
                  :getCalendarContainer="getPopupContainer()" />
              </a-form-item>
            </a-config-provider>
          </a-col>
        </a-row>
      </div>
    </template>
    <template v-else>
      <div class="base_info">
        <div>
          <a-row v-if="!userInfo.roleKeyList.includes('ecologicalPartner')">
            <a-col :span="12">
              <a-form-item label="项目名称">
                {{ localFormData.projectName }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="localFormData.projectCodeType == 2 ? '商机编码' : '项目编码'">
                {{ localFormData.projectCode || '-' }}
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <a-row>
          <a-col :span="12">
            <a-form-item label="需求发起人">
              {{ localFormData.userInfo || '-' }}
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系方式">
              {{ localFormData.userPhone || '-' }}
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="支撑方式">
              {{ localFormData.supportMehod == 1 ? '远程支撑' : localFormData.supportMehod == 2 ? '现场支撑' : '-' }}
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="支撑时限">
              {{ formatTime(localFormData.time) }}
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </template>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
// import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import SectionTitle from '../SectionTitle/index.vue';

export default defineComponent({
  name: 'BasicInfoForm',
  components: {
    SectionTitle,
    // 'a-config-provider': ConfigProvider
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    projectOptions: {
      type: Array,
      default: () => []
    },
    projectNameFetching: {
      type: Boolean,
      default: false
    },
    projectCodeFetching: {
      type: Boolean,
      default: false
    },
    userInfo: {
      type: Object,
      default: () => ({ roleKeyList: [] })
    }
  },
  emits: ['update:formData', 'project-code-type-change', 'project-name-change', 'project-code-change'],
  setup(props, { emit }) {
    const localFormData = ref({ ...props.formData });
    const zhCNLocale = ref(zhCN);

    // 监听外部formData变化
    watch(() => props.formData, (newVal) => {
      localFormData.value = { ...newVal };
    }, { deep: true });

    // 监听本地formData变化并向外发送
    watch(localFormData, (newVal) => {
      emit('update:formData', newVal);
    }, { deep: true });

    const disabledDate = (current) => {
      const today = dayjs().startOf("day");
      return current && current < today.add(3, 'days');
    };

    const formatTime = (time) => {
      if (!time) return "-";
      const normalizedTime = time.replace(/[年月]/g, '-').replace('日', '');
      return dayjs(normalizedTime).format('YYYY年MM月DD日');
    };

    const getPopupContainer = () => {
      return triggerNode => triggerNode.parentNode;
    };

    const handleProjectCodeTypeChange = () => {
      localFormData.value.projectName = undefined;
      localFormData.value.projectCode = undefined;
      emit('project-code-type-change');
    };

    const handleProjectNameChange = (value) => {
      const selectedItem = props.projectOptions.find(item => item.name === value);
      if (selectedItem) {
        localFormData.value.projectCode = selectedItem.code;
      } else {
        localFormData.value.projectCode = undefined;
      }
      emit('project-name-change', value);
    };

    const handleProjectCodeChange = (value) => {
      const selectedItem = props.projectOptions.find(item => item.code === value);
      if (selectedItem) {
        localFormData.value.projectName = selectedItem.name;
      } else {
        localFormData.value.projectName = undefined;
      }
      emit('project-code-change', value);
    };

    const handleLimitLength = (e) => {
      let value = e.target.value;
      // 项目编码和商机编码可以输入英文和数字
      value = value.replace(/[^a-zA-Z0-9]/g, '');
      if (value.length > 30) {
        value = value.slice(0, 30);
      }
      localFormData.value.projectCode = value;
    };

    return {
      localFormData,
      zhCN: zhCNLocale,
      disabledDate,
      formatTime,
      getPopupContainer,
      handleProjectCodeTypeChange,
      handleProjectNameChange,
      handleProjectCodeChange,
      handleLimitLength
    };
  }
});
</script>

<style lang="scss" scoped>
.basic-info-form {
  .base_info {
    margin: 10px 24px 10px 32px;
    padding: 24px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #D1D0D8;
    display: grid;
  }
}

:deep(.ant-form-item-label) {
  width: 122px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}
</style>
