<template>
  <a-modal :visible="visible" title="提示" @ok="handleConfirm" @cancel="handleCancel" okText="确认" cancelText="取消"
    width="400px">
    <p>是否确认选择能力方已支撑，点击确定将进行生态评分</p>
  </a-modal>
</template>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ConfirmSupportedModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const handleConfirm = () => {
      emit('confirm');
    };

    const handleCancel = () => {
      emit('update:visible', false);
      emit('cancel');
    };

    return {
      handleConfirm,
      handleCancel
    };
  }
});
</script>

<style lang="scss" scoped>
// 这个组件使用默认的 ant-design-vue 样式，不需要额外样式</style>
