<template>
  <a-modal :visible="visible" @cancel="handleClose" :title="title" width="1200px" centered :destroyOnClose="true"
    :maskClosable="false" :footer="null">
    <GuideTable :sourceType="sourceType" @close="handleClose" @ok="handleSelect" />
  </a-modal>
</template>

<script>
import { defineComponent } from 'vue';
import GuideTable from '../GuideTable/index.vue';

export default defineComponent({
  name: 'ModuleTypeModal',
  components: {
    GuideTable
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择业务模块'
    },
    sourceType: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible', 'select'],
  setup(props, { emit }) {
    const handleClose = () => {
      emit('update:visible', false);
    };

    const handleSelect = (value) => {
      emit('select', value);
    };

    return {
      handleClose,
      handleSelect
    };
  }
});
</script>

<style lang="scss" scoped>
// 如果需要特定样式可以在这里添加
</style>
