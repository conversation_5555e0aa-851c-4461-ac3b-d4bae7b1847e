<template>
  <a-modal :visible="visible" title="提示" :footer="null" @cancel="handleClose" width="40%">
    <a-form ref="formRef" :model="formData" labelAlign="right" :rules="rules" :colon="false">
      <a-row>
        <a-col :span="24">
          <a-form-item label="是否转售中" name="isTurn">
            <a-radio-group v-model:value="formData.isTurn">
              <a-radio value="1">是</a-radio>
              <a-radio value="2">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row v-if="isSubmit">
        <a-col :span="24">
          <a-form-item :label="projectNameLabel" name="projectName">
            <a-input v-model:value="formData.projectName" placeholder="请填写DICT项目管理系统项目名称"
              :disabled="projectType.isProject" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row v-if="isSubmit">
        <a-col :span="24">
          <a-form-item :label="projectCodeLabel" name="projectCode">
            <a-input type="text" v-model:value="formData.projectCode" :placeholder="projectCodePlaceholder"
              @input="handleProjectCodeInput" :disabled="projectType.isProject" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row v-if="isSubmit">
        <a-col :span="24">
          <a-form-item label="交付经理" name="deliveryManager">
            <a-select placeholder="请选择交付经理" v-model:value="formData.deliveryManager" allowClear show-search
              :filter-option="filterOption">
              <template v-for="(opt, index) in personList" :key="index">
                <a-select-option :value="String(opt.id)" :label="opt.realName">
                  {{ opt.realName }}
                </a-select-option>
              </template>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <div style="display: flex; padding-left: 65px; color: #a2abb5">
      <p>备注</p>
      <p>选择是将发送短信给交付经理</p>
    </div>

    <div class="modal-footer">
      <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleClose">取消</a-button>
      <a-button class="custom_btn active_btn" @click="handleSubmit">确认</a-button>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch, computed } from 'vue';

export default defineComponent({
  name: 'PostScoreModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    personList: {
      type: Array,
      default: () => []
    },
    projectCodeType: {
      type: Number,
      default: 1
    },
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const formRef = ref();
    const formData = ref({
      isTurn: '2',
      projectName: undefined,
      projectCode: undefined,
      deliveryManager: undefined
    });
    const projectType = computed(() => {
      return {
        isProject: props.projectCodeType === 1,
        isBusiness: props.projectCodeType === 2
      };
    });

    const isSubmit = computed(() => {
      return formData.value.isTurn === '1';
    });

    const projectCodeLabel = computed(() => {
      return projectType.value.isBusiness ? '商机编码' : '项目编码';
    });
    const projectNameLabel = computed(() => {
      return projectType.value.isBusiness ? '商机名称' : '项目名称';
    });

    const projectCodePlaceholder = computed(() => {
      return projectType.value.isBusiness
        ? '请填写DICT项目管理系统商机编码'
        : '请填写DICT项目管理系统项目编码';
    });

    const rules = computed(() => {
      const baseRules = {
        isTurn: [
          { required: true, message: '请选择是否转售中', trigger: 'change' }
        ]
      };

      if (isSubmit.value) {
        baseRules.projectName = [
          { required: true, message: '请填写项目名称', trigger: 'blur' }
        ];
        baseRules.projectCode = [
          { required: true, message: `请填写${projectCodeLabel.value}`, trigger: 'blur' }
        ];
        baseRules.deliveryManager = [
          { required: true, message: '请选择交付经理', trigger: 'change' }
        ];
      }

      return baseRules;
    });

    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        // 重置或初始化表单数据
        formData.value = {
          isTurn: props.initialData.isTurn || undefined,
          projectName: props.initialData.projectName || undefined,
          projectCode: props.initialData.projectCode || undefined,
          deliveryManager: props.initialData.deliveryManager || undefined
        };
      }
    });

    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    const handleProjectCodeInput = (e) => {
      let value = e.target.value;
      // 项目编码和商机编码可以输入英文和数字
      value = value.replace(/[^a-zA-Z0-9]/g, '');
      if (value.length > 30) {
        value = value.slice(0, 30);
      }
      formData.value.projectCode = value;
    };

    const handleClose = () => {
      emit('update:visible', false);
    };

    const handleSubmit = async () => {
      try {
        await formRef.value?.validate();
        emit('submit', formData.value);
      } catch (error) {
        console.log('Validation failed:', error);
      }
    };

    return {
      formRef,
      formData,
      rules,
      isSubmit,
      projectCodeLabel,
      projectNameLabel,
      projectType,
      projectCodePlaceholder,
      filterOption,
      handleProjectCodeInput,
      handleClose,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/button.scss";

.modal-footer {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}

:deep(.ant-form-item-label) {
  width: 122px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}
</style>
