<template>
  <a-modal :visible="visible" title="未支撑确认" :footer="null" @cancel="handleClose" width="50%">
    <a-form ref="formRef" :model="formData" labelAlign="right" :rules="rules" :colon="false" style="margin-top:10px;">
      <a-form-item label="拒绝理由类型" name="rejectType">
        <a-select v-model:value="formData.rejectType" placeholder="请选择拒绝理由类型" style="width: 60%">
          <a-select-option value="1">拒绝支撑（计划自己直接中标）</a-select-option>
          <a-select-option value="2">拒绝支撑（已与友商合作）</a-select-option>
          <a-select-option value="3">不具备本次调度方案相关的支撑能力</a-select-option>
          <a-select-option value="4">超出生态支撑地域范围</a-select-option>
          <a-select-option value="5">其他</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item v-if="formData.rejectType === '5'" label="拒绝备注" name="suggest">
        <a-textarea :rows="7" :showCount="true" :maxlength="100" placeholder="请输入请输入拒绝备注，限制100个字符"
          v-model:value="formData.suggest" />
      </a-form-item>
    </a-form>

    <div class="modal-footer">
      <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleClose">取消</a-button>
      <a-button class="custom_btn active_btn" @click="handleSubmit" :loading="loading">确认</a-button>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
  name: 'RejectTypeModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const formRef = ref();
    const formData = ref({
      rejectType: undefined,
      suggest: undefined
    });

    const rules = {
      rejectType: [
        { required: true, message: '请选择拒绝理由类型', trigger: 'change' }
      ],
      suggest: [
        { required: true, message: '请输入拒绝备注', trigger: 'blur' }
      ]
    };

    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        // 重置或初始化表单数据
        formData.value = {
          rejectType: props.initialData.rejectType || undefined,
          suggest: props.initialData.suggest || undefined
        };
      }
    });

    const handleClose = () => {
      emit('update:visible', false);
    };

    const handleSubmit = async () => {
      try {
        await formRef.value?.validate();
        emit('submit', formData.value);
      } catch (error) {
        console.log('Validation failed:', error);
      }
    };

    return {
      formRef,
      formData,
      rules,
      handleClose,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/button.scss";

.modal-footer {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}

:deep(.ant-form-item-label) {
  width: 122px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}
</style>
