<template>
  <p :class="titleClasses" :style="titleStyles">
    <span class="icon"></span>
    <slot>{{ title }}</slot>
  </p>
</template>

<script>
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'SectionTitle',
  props: {
    // 标题文本
    title: {
      type: String,
      default: ''
    },
    // 自定义CSS类
    customClass: {
      type: String,
      default: ''
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    },
  },
  setup(props) {
    // 计算标题的CSS类
    const titleClasses = computed(() => {
      const baseClasses = ['group_title', 'weight500', 'font_14', 'font_00060e'];

      // 添加自定义类
      if (props.customClass) {
        baseClasses.push(props.customClass);
      }

      return baseClasses.join(' ');
    });

    // 计算标题的内联样式
    const titleStyles = computed(() => {
      const styles = { ...props.customStyle };

      return styles;
    });

    return {
      titleClasses,
      titleStyles,
    };
  }
});
</script>

<style lang="scss" scoped>
.group_title {
  // margin: 0;
  // padding: 0;

  .icon {
    display: inline-block;
    width: 4px;
    height: 13px;
    background: #0c70eb;
    box-shadow: 2px 1px 6px 0px rgba(12, 112, 235, 0.5),
      inset 0px 0px 3px 0px rgba(255, 255, 255, 0.8);
    border-radius: 2px 2px 2px 2px;
    margin-right: 8px;
  }
}
</style>
