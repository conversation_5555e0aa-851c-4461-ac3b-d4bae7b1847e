<template>
  <a-modal :visible="visible" title="支撑反馈" :footer="null" @cancel="handleClose" width="50%">
    <a-form ref="formRef" :model="formData" labelAlign="right" :rules="rules" :colon="false">
      <a-form-item v-if="!isRejectMode && !isThirdEco" label="支撑人/天数" name="supportCos">
        <a-input v-model:value="formData.supportCos" placeholder="请填写支撑人/天数" />
      </a-form-item>

      <a-form-item v-if="isRejectMode" label="拒绝理由类型" name="rejectType">
        <a-select v-model:value="formData.rejectType" placeholder="请选择拒绝理由类型" style="width: 60%">
          <a-select-option value="1">拒绝支撑（计划自己直接中标）</a-select-option>
          <a-select-option value="2">拒绝支撑（已与友商合作）</a-select-option>
          <a-select-option value="3">不具备本次调度方案相关的支撑能力</a-select-option>
          <a-select-option value="4">超出生态支撑地域范围</a-select-option>
          <a-select-option value="5">其他</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item v-if="!isRejectMode || (isRejectMode && formData.rejectType === '5')"
        :label="isRejectMode ? '拒绝原因' : '实际支撑结果'" name="suggest">
        <a-textarea :rows="7" :showCount="true" :maxlength="isRejectMode ? 100 : 500"
          :placeholder="'请输入' + (isRejectMode ? '请输入拒绝备注，限制100个字符' : '实际支撑结果内容，限制500个字符')"
          v-model:value="formData.suggest" />
      </a-form-item>

      <a-form-item v-if="!isRejectMode" label="附件">
        <FileUpload :customClassName="'custom_btn active_btn'" :fileInfo="{
          type: 'other',
          fileList: formData.fileListDeal,
          index: null,
          subIndex: null,
          accept: '.doc,.docx,.ppt,.pptx',
          required: false,
          category: '2',
          multiple: true,
          acceptLength: 1,
          size: 150,
          mark: '仅支持ppt,doc,docx格式的文件上传(文件个数限1个,单个文件限150M）',
        }" @update-file="handleFileUpdate" @update-load="handleFileLoad" />
      </a-form-item>
    </a-form>

    <div class="modal-footer">
      <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleClose">取消</a-button>
      <a-button class="custom_btn active_btn" @click="handleSubmit" :loading="loading">确认</a-button>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch, computed } from 'vue';
import FileUpload from "@/components/fileUpload/fileUpload.vue";

export default defineComponent({
  name: 'SupportFeedbackModal',
  components: {
    FileUpload
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isRejectMode: {
      type: Boolean,
      default: false
    },
    isThirdEco: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'submit', 'file-update', 'file-load'],
  setup(props, { emit }) {
    const formRef = ref();
    const formData = ref({
      supportCos: undefined,
      rejectType: undefined,
      suggest: undefined,
      fileListDeal: []
    });

    const rules = computed(() => {
      const baseRules = {
        suggest: [
          { required: true, message: `请输入${props.isRejectMode ? '拒绝原因' : '实际支撑结果'}`, trigger: 'blur' }
        ]
      };

      if (!props.isRejectMode && !props.isThirdEco) {
        baseRules.supportCos = [
          { required: true, message: '请填写支撑人/天数', trigger: 'blur' }
        ];
      }

      if (props.isRejectMode) {
        baseRules.rejectType = [
          { required: true, message: '请选择拒绝理由类型', trigger: 'change' }
        ];
      }

      return baseRules;
    });

    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        // 重置或初始化表单数据
        formData.value = {
          supportCos: props.initialData.supportCos || undefined,
          rejectType: props.initialData.rejectType || undefined,
          suggest: props.initialData.suggest || undefined,
          fileListDeal: props.initialData.fileListDeal || []
        };
      }
    });

    const handleFileUpdate = (fileInfo) => {
      formData.value.fileListDeal = fileInfo.fileList;
      emit('file-update', fileInfo);
    };

    const handleFileLoad = (loadInfo) => {
      emit('file-load', loadInfo);
    };

    const handleClose = () => {
      emit('update:visible', false);
    };

    const handleSubmit = async () => {
      try {
        await formRef.value?.validate();
        emit('submit', formData.value);
      } catch (error) {
        console.log('Validation failed:', error);
      }
    };

    return {
      formRef,
      formData,
      rules,
      handleFileUpdate,
      handleFileLoad,
      handleClose,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/button.scss";

.modal-footer {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}

:deep(.ant-form-item-label) {
  width: 122px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.file-list) {
  width: 100%;
}
</style>
