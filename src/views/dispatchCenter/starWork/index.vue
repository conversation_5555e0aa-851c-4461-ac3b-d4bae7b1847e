<template>
  <div style="display: flex;justify-content: center;margin-top: 20px;">
    <div id="addAbilit" class="background_fff" style="width:1200px">
      <div>
        <div v-if="viewLoading" class="loading-overlay">
          <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
        </div>
        <div v-if="formLoading" class="loading-overlay">
          <a-spin :spinning="formLoading" tip="数据加载中..." />
        </div>
        <div v-if="action === 'edit'" class="form-title-class">发起售前调度支撑</div>
        <a-form ref="mainFormRef" :model="formData" labelAlign="right" :rules="rules"
          class="operation padding_l_24 padding_t_24" :colon="false">
          <SectionTitle v-if="action !== 'edit'" title="工单信息" />
          <div v-if="action !== 'edit'">
            <a-row>
              <a-col :span="23">
                <a-form-item label="工单标题">
                  {{ formData.title }}
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <BasicInfoForm
            :form-data="formData"
            :is-edit-mode="action === 'edit'"
            :project-options="projectOptions"
            :user-info="userInfo"
            @update:form-data="handleFormDataUpdate"
          />
          <SectionTitle v-if="action == 'edit'" title="售前调度流程" />
          <div v-for="(moduleItem, moduleIndex) in formData.moduleForm" :key="moduleItem.uid">
            <div class="module_title">
              <p class="weight500 font_14 font_00060e" style="margin-bottom:0">支撑需求{{ toChinese(moduleIndex + 1) }}</p>
              <a-button v-if="formData.moduleForm.length > 1 && action == 'edit'"
                class="button_btn custom_btn reject_btn" @click="handleDeleteModule(moduleIndex)"
                :disabled="moduleExistInQuery(moduleItem.uid) && !fromOneKeyDispatch()">删除需求</a-button>
            </div>
            <div class="module_group">
              <div
                v-if="support_type == '省级' && (moduleItem.status == 'toSelect' || moduleItem.status == 'reSelectPage') && !contralSupport"
                style="display: flex;">
                <a-form-item label="是否自己支撑" style="margin-bottom: 0;" class="switch-label">
                  <a-radio-group v-model:value="moduleItem.isBySelf">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </div>
              <div
                v-if="((contralSupport || support_type == '市级') && (moduleItem.status == 'toSelect' || (moduleItem.status == 'reSelectPage' && moduleItem.textList.some(text => text.dealType == '1' || text.dealType == '2'))))"
                style="display: flex;margin-bottom: 24px;height: 32px;">
                <div class="provinceBtn" style="margin-right: 46px; align-items: center">
                  <p style="margin-bottom: 0;" class="switch-label">申请省级调度支撑</p>
                  <a-switch v-model:checked="moduleItem.needProvince"
                    @change="(value) => controlPro(value, moduleIndex)" />
                </div>
                <div v-if="moduleItem.needProvince" style="display: flex; margin-bottom: 0" class="select_pro">
                  <p style="margin-bottom: 0; margin-right: 12px">选择省级调度管理员</p>
                  <a-select placeholder="请选择调度管理员" v-model:value="moduleItem.provinceUser" allowClear
                    :getPopupContainer="getPopupContainer()">
                    <template v-for="opt in provincePersonList" :key="opt.id">
                      <a-select-option :value="opt.id">
                        {{ formatRealName(opt.realName, opt.remark) }}
                      </a-select-option>
                    </template>
                  </a-select>
                </div>
              </div>
              <a-row v-if="action == 'edit' || moduleItem.status == 'reStar'">
                <a-col :span="24">
                  <a-form-item label="支撑需求描述" :name="['moduleForm', moduleIndex, 'projectContent']"
                    :rules="[{ required: moduleIndex == 0 ? true : false, message: '请输入支撑需求描述', trigger: 'blur' }]">
                    <a-textarea :rows="7" :showCount="true" :maxlength="500"
                      style="border-radius: 8px;margin-bottom:20px;"
                      placeholder="包含项目地点，具体需要支撑的内容，内容中切勿透露完整的项目名称和客户基本信息等，防止商机泄露。限制500个字以内"
                      v-model:value="moduleItem.projectContent"></a-textarea>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item label="支撑需求描述" style="word-wrap: break-word; word-break: break-all;">
                    {{ moduleItem.projectContent || '-' }}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="action != 'edit'">
                <a-col :span="24">
                  <a-form-item label="支撑时限" style="word-wrap: break-word; word-break: break-all;">
                    {{ formatTime(moduleItem.time) }}
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item v-if="action == 'edit' || moduleItem.status == 'reStar'" label="附件">
                <FileUpload :customClassName="'custom_btn active_btn'" :fileInfo="{
                  type: 'other',
                  fileList: moduleItem.fileList,
                  index: null,
                  subIndex: null,
                  accept: '.doc,.docx,.ppt,.pptx',
                  required: false,
                  category: '2',
                  multiple: true,
                  acceptLength: 1,
                  size: 150,
                  mark: '仅支持ppt,doc,docx格式的文件上传(文件个数限1个,单个文件限150M）',
                }" @update-file="(fileInfo) => setFileData(fileInfo, moduleItem)" @update-load="viewFileData" />
              </a-form-item>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item label="附件">
                    <div class="file-list" v-if="moduleItem.fileList.length != 0">
                      <div class="flex">
                        <p>
                          <span>
                            <i class="iconfont icon-annex"></i>
                            <span>
                              &nbsp;{{ moduleItem.fileList[0]?.name }} &nbsp;</span>
                          </span>
                        </p>
                        <div class="font_0c70eb">
                          <span @click="view(moduleItem.fileList[0])">
                            &nbsp;预览</span>
                          <span @click="download(moduleItem.fileList[0])">
                            &nbsp;下载</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>暂无附件</div>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item v-if="action == 'edit' || moduleItem.status == 'reStar'" label="业务模块选择">
                <a-radio-group v-model:value="moduleItem.contentType"
                  @change="(val) => changeBusinessModuleType(val, moduleIndex)"
                  :disabled="moduleExistInQuery(moduleItem.uid)">
                  <a-radio v-for="item in moduleTypeList" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
              <div>
                <p class="switch-label">{{ businessModuleTypeComputed(moduleItem.contentType) }}信息</p>
                <div class="abInfo no-margin-left-right">
                  <div class="top">
                    <span style="width:107px;display: inline-block;text-align: right;margin-right:12px;"
                      class="weight500 font_14 font_00060e">
                      {{ businessModuleTypeComputed(moduleItem.contentType) }}名称</span>
                    <template v-if="action == 'edit' || moduleItem.status == 'reStar'">
                      <a-input disabled v-model:value="moduleItem.name"
                        :placeholder="'请选择' + businessModuleTypeComputed(moduleItem.contentType)" style="width: 300px"
                        allowClear></a-input>
                      <a-button style="margin-left: 10px;" class="custom_btn active_btn"
                        @click="() => showModuleTypeDialog(moduleIndex)"
                        :disabled="moduleExistInQuery(moduleItem.uid)">选择</a-button>
                    </template>
                    <span v-else>
                      {{ moduleItem.name || '-' }}
                    </span>
                  </div>
                  <div class="center">
                    <span style="width:107px;margin-top: 24px;text-align: right;vertical-align: top;margin-right:12px;"
                      class="weight500 font_14 font_00060e">
                      {{ businessModuleTypeComputed(moduleItem.contentType) }}简介
                    </span>
                    <p v-if="action == 'edit' || moduleItem.status == 'reStar'" class="intro-class">{{ moduleItem.intro
                      }}</p>
                    <p v-else class="intro-class">{{ moduleItem.intro }}</p>
                  </div>
                </div>
              </div>
              <!-- 生态厂商反馈的信息 -->
              <el-table v-if="moduleItem.status == 'toSelect' && moduleItem.textList.length > 0"
                :data="moduleItem.textList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px" class="scrollable-table">
                <el-table-column label="自有能力方/生态厂商">
                  <template #default="scope">
                    <span style="margin-right: 12px; display: inline-block">
                      {{ scope.row.company }}&nbsp;&nbsp;
                      {{ scope.row.contactUserName }}&nbsp;&nbsp;
                      {{ scope.row.contactPhone }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="是否支撑">
                  <template #default="scope">
                    {{ actionIsSupport(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column prop="dealTime" label="回复时间"></el-table-column>
              </el-table>
              <div v-if="action == 'edit' && moduleItem.ecopartnerList?.[0]?.company?.length > 0"
                class="edit-eco-table-wrap">
                <el-table :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px; margin-bottom: 20px;">
                  <el-table-column v-if="!moduleItem.ecologyType" prop="company" label="生态厂商" />
                  <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('2')" prop="company"
                    label="生态厂商">
                    <template #header>
                      <div class="flex-center-align">
                        <span>生态厂商</span>
                        <a-button class="custom_btn active_btn add_btn"
                          @click="addCooperate(moduleItem.ecopartnerList[0], moduleIndex)">新增生态厂商</a-button>
                      </div>
                    </template>
                    <template #default="scope">
                      <div class="table-row-wrapper" style="overflow-x: auto;">
                        <div class="search-wrapper search-input-wrap">
                          <a-input v-model:value="moduleItem.searchForm.ecopartnerName" placeholder="请输入公司名称或联系人姓名"
                            allowClear style="width: 300px" />
                          <div>
                            <a-button class="custom_btn active_btn"
                              @click="handleSearchCompany(moduleItem)">搜索</a-button>
                          </div>
                        </div>
                        <div v-for="(companyItem, index) in filterCompanyList(moduleItem, scope.row.company)"
                          :key="index" class="box person-wrap">
                          <div class="person-container">
                            <a-radio-group :value="moduleItem.selectId">
                              <a-radio :value="companyItem.contactPhone"
                                @change="(e) => onCheckChange(e, companyItem, moduleIndex)"
                                :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                              </a-radio>
                            </a-radio-group>
                            <span v-if="companyItem.ecopartnerName" class="company_left company-name font-weight-500"
                              @click="toCompanyDetail(companyItem)">
                              <span class="company_underline">{{ companyItem.ecopartnerName }}</span>
                              <span
                                v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve && companyItem.approve != 1"
                                class="warning-text">生态厂商暂无该生态联系人！</span>
                              <span v-else-if="companyItem.auth == 0"
                                class="warning-text">该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！</span>
                              <span v-else-if="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve)"
                                class="warning-text">生态厂商暂无该生态联系人！</span>
                            </span>
                          </div>
                          <div class="company_right" style="display: inline-flex; align-items: center; ">
                            <img width="20px" height="20px" style="margin-bottom: 0;" src="@/assets/images/score.png" />
                            <span>生态评分</span>
                            <span class="score-font-class">{{ formatScore(companyItem) }}</span>
                          </div>
                          <a-select v-model:value="companyItem.contactName"
                            @change="(value) => selectUserCom(value, companyItem)"
                            :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)"
                            :getPopupContainer="triggerNode => triggerNode.closest('.edit-eco-table-wrap')">
                            <template v-for="(opt, index) in companyItem.contactList" :key="index">
                              <a-select-option :value="opt.userId" :label="opt.contactName"
                                :disabled="opt.approve != 1">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span
                            :style="{ color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999' }">
                            {{ companyItem.contactPhone }}
                          </span>
                        </div>
                        <div v-if="filterCompanyList(moduleItem, scope.row.company).length == 0">暂无数据</div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-table v-if="action == 'edit' && moduleItem.ecopartnerList?.[0]?.ownPerson?.length > 0"
                :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column v-if="!moduleItem.ecologyType" prop="ownPerson" label="自有能力方" />
                <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('1')" prop="ownPerson"
                  label="自有能力方">
                  <template #default="scope">
                    <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box person-wrap">
                      <div style="display: flex;width:200px;">
                        <a-radio-group :value="moduleItem.selectIdOwn">
                          <a-radio :value="ownPersonItem.contactPhone"
                            @change="(e) => onCheckChange(e, ownPersonItem, moduleIndex)">
                          </a-radio>
                        </a-radio-group>
                        <span class="font-weight-500">
                          {{ ownPersonItem.belong }}
                        </span>
                      </div>
                      <span class="contactName">
                        {{ ownPersonItem.contactName }}
                      </span>
                      <p class="contactPhone">
                        {{ ownPersonItem.contactPhone }}
                      </p>
                    </div>
                    <div v-if="scope.row.ownPerson.length == 0">暂无数据</div>
                  </template>
                </el-table-column>
              </el-table>
              <div
                v-if="moduleItem.status == 'toSelect' && !moduleItem.needProvince && moduleItem.isBySelf == '2' && moduleItem.ecopartnerList?.[0]?.company?.length > 0"
                class="toselect-eco-table-wrap">
                <el-table :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }" border style="width: 100%; margin-top: 20px;">
                  <el-table-column v-if="!moduleItem.ecologyType" prop="company" label="生态厂商" />
                  <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('2')" prop="company"
                    label="生态厂商">
                    <template #header>
                      <div class="flex-center-align">
                        <span>生态厂商</span>
                        <a-button class="custom_btn active_btn add_btn"
                          @click="addCooperate(moduleItem.ecopartnerList[0], moduleIndex)">新增生态厂商</a-button>
                      </div>
                    </template>
                    <template #default="scope">
                      <div class="table-row-wrapper" style="overflow-x: auto;">
                        <div class="search-wrapper search-input-wrap">
                          <a-input v-model:value="moduleItem.searchForm.ecopartnerName" placeholder="请输入公司名称或联系人姓名"
                            allowClear style="width: 300px" />
                          <div>
                            <a-button class="custom_btn active_btn"
                              @click="handleSearchCompany(moduleItem)">搜索</a-button>
                          </div>
                        </div>
                        <div v-for="(companyItem, index) in filterCompanyList(moduleItem, scope.row.company)"
                          :key="index" class="box person-wrap">
                          <div class="person-container">
                            <a-radio-group :value="moduleItem.selectId">
                              <a-radio :value="companyItem.contactPhone"
                                @change="(e) => onCheckChange(e, companyItem, moduleIndex)"
                                :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                              </a-radio>
                            </a-radio-group>
                            <span v-if="companyItem.ecopartnerName" class="company_left company-name font-weight-500"
                              @click="toCompanyDetail(companyItem)">
                              <span class="company_underline">{{ companyItem.ecopartnerName }}</span>
                              <span
                                v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve && companyItem.approve != 1"
                                class="warning-text">生态厂商暂无该生态联系人！</span>
                              <span v-else-if="companyItem.auth == 0"
                                class="warning-text">该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！</span>
                              <span v-else-if="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve)"
                                class="warning-text">生态厂商暂无该生态联系人！</span>
                            </span>
                          </div>
                          <div class="company_right" style="display: inline-flex; align-items: center; ">
                            <img width="20px" height="20px" style="margin-bottom: 0;" src="@/assets/images/score.png" />
                            <span>生态评分</span>
                            <span class="score-font-class">{{ formatScore(companyItem) }}</span>
                          </div>
                          <a-select v-model:value="companyItem.contactName"
                            @change="(value) => selectUserCom(value, companyItem)"
                            :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)"
                            :getPopupContainer="triggerNode => triggerNode.closest('.toselect-eco-table-wrap')">
                            <template v-for="(opt, index) in companyItem.contactList" :key="index">
                              <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span
                            :style="{ color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999' }">
                            {{ companyItem.contactPhone }}
                          </span>
                        </div>
                        <div v-if="filterCompanyList(moduleItem, scope.row.company).length == 0">暂无数据</div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-table
                v-if="moduleItem.status == 'toSelect' && !moduleItem.needProvince && moduleItem.isBySelf == '2' && moduleItem.ecopartnerList?.[0]?.ownPerson?.length > 0"
                :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ textAlign: 'center' }" border style="width: 100%; margin-top: 20px;">
                <el-table-column v-if="!moduleItem.ecologyType" prop="ownPerson" label="自有能力方" />
                <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('1')" prop="ownPerson"
                  label="自有能力方">
                  <template #default="scope">
                    <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box person-wrap">
                      <div style="display: flex;width:200px;">
                        <a-radio-group :value="moduleItem.selectIdOwn">
                          <a-radio :value="ownPersonItem.contactPhone"
                            @change="(e) => onCheckChange(e, ownPersonItem, moduleIndex)">
                          </a-radio>
                        </a-radio-group>
                        <span class="font-weight-500">
                          {{ ownPersonItem.belong }}
                        </span>
                      </div>
                      <span class="contactName">
                        {{ ownPersonItem.contactName }}
                      </span>
                      <p class="contactPhone">
                        {{ ownPersonItem.contactPhone }}
                      </p>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-table
                v-if="moduleItem.status == 'toSelect' && !moduleItem.needProvince && moduleItem.isBySelf == '2' && (support_type == '省级' && !contralSupport && moduleItem.ecopartnerList?.[0]?.ownProvince?.length > 0)"
                :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ textAlign: 'center' }" border style="width: 100%; margin-top: 20px;">
                <el-table-column prop="ownProvince" label="方案/能力联系人">
                  <template #default="scope">
                    <div v-for="(ownProvinceItem, index) in scope.row.ownProvince" :key="index" class="box person-wrap">
                      <div style="display: flex;width:200px;">
                        <a-radio-group :value="moduleItem.selectIdOwn">
                          <a-radio :value="ownProvinceItem.belong"
                            @change="(e) => onCheckChange(e, ownProvinceItem, moduleIndex)">
                          </a-radio>
                        </a-radio-group>
                        <span class="font-weight-500">
                          {{ ownProvinceItem.belong }}
                        </span>
                      </div>
                      <span class="contactName">
                        {{ ownProvinceItem.contactName }}
                      </span>
                      <p class="contactPhone">
                        {{ ownProvinceItem.contactPhone }}
                      </p>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 重新调度 -->
              <el-table v-if="moduleItem.status == 'reSelectPage'" :data="moduleItem.reSelectData" :empty-text="'暂无数据'"
                :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                <el-table-column prop="info" label="自有能力方/生态厂商" width="480">
                  <template #default="scope">
                    {{ scope.row.info.ecopartnerName }}
                    {{ scope.row.info.contactName }}
                    {{ scope.row.info.contactPhone }}
                  </template>
                </el-table-column>
                <el-table-column prop="dealContent" :show-overflow-tooltip="true" label="是否支撑">
                  <template #default="scope">
                    <el-tooltip v-if="scope.row.dealContent && scope.row.dealContent.length > 60" trigger="hover"
                      :content="scope.row.dealContent" placement="top" popper-class="custom-tooltip">
                      <p class="content_control">
                        {{ scope.row.dealContent }}
                      </p>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="rejectReason" label="支撑结果" />
                <el-table-column prop="dealTime" label="回复时间" />
              </el-table>
              <template
                v-if="moduleItem.textList && moduleItem.textList.length > 0 && action != 'edit' && moduleItem.status != 'selectApply' && moduleItem.status != 'toSelect' && moduleItem.status != 'reSelectPage'">
                <SupportInfoDescriptions v-for="(textItem, index) in moduleItem.textList" :key="index"
                  :support-info="textItem" :module-status="moduleItem.status" @preview-file="view"
                  @download-file="download" @score-change="handleScoreChange"
                  @evaluation-change="handleEvaluationChange" />
              </template>
              <p v-if="moduleItem.status != 'dealedAll' && moduleItem && moduleItem.status == 'timeoutReject'"
                class="group_title weight500 font_14 font_00060e" style="margin-top:20px;">
                请选择该能力方是否支撑
                <a-radio-group v-model:value="moduleItem.isSupported"
                  style="display:inline-block;font-weight:normal;margin-left:12px;"
                  @change="(val) => onSupportChange(val, moduleIndex)">
                  <a-radio value="0">未支撑</a-radio>
                  <a-radio value="1">已支撑</a-radio>
                </a-radio-group>
              </p>
              <p v-if="moduleItem.status != 'dealedAll' && moduleItem.isSupported == '0' && moduleItem.status == 'timeoutReject'"
                class="group_title weight500 font_14 font_00060e" style="margin-top:20px">
                是否重新派单
                <a-radio-group v-model:value="moduleItem.isReassign"
                  style="display:inline-block;font-weight:normal;margin-left:12px;">
                  <a-radio value="0">重新派单</a-radio>
                  <a-radio value="1">结单</a-radio>
                </a-radio-group>
              </p>
              <div
                v-if="moduleItem.status != 'dealedAll' && (moduleItem.status == 'reStar' || (moduleItem.status == 'timeoutReject' && moduleItem.isReassign == '0') || (moduleItem.status == 'reSelectPage' && (!moduleItem.needProvince && moduleItem.isBySelf == '2' && (moduleItem.textList.some(text => text.dealType == '1' || text.dealType == '2')))))"
                class="re-select-eco-table-wrap">
                <el-table v-if="moduleItem.ecopartnerList?.[0]?.company?.length > 0" :data="moduleItem.ecopartnerList"
                  :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                  <el-table-column v-if="!moduleItem.ecologyType" prop="company" label="生态厂商" />
                  <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('2')" prop="company"
                    label="生态厂商">
                    <template #header>
                      <div class="flex-center-align">
                        <span>生态厂商</span>
                        <a-button class="custom_btn active_btn add_btn"
                          @click="addCooperate(moduleItem.ecopartnerList[0], moduleIndex)">新增生态厂商</a-button>
                      </div>
                    </template>
                    <template #default="scope">
                      <div class="table-row-wrapper" style="overflow-x: auto;">
                        <div class="search-wrapper search-input-wrap">
                          <a-input v-model:value="moduleItem.searchForm.ecopartnerName" placeholder="请输入公司名称或联系人姓名"
                            allowClear style="width: 300px" />
                          <div>
                            <a-button class="custom_btn active_btn"
                              @click="handleSearchCompany(moduleItem)">搜索</a-button>
                          </div>
                        </div>
                        <div v-for="(companyItem, index) in filterCompanyList(moduleItem, scope.row.company)"
                          :key="index" class="box person-wrap">
                          <div class="person-container">
                            <a-radio-group :value="moduleItem.selectId">
                              <a-radio :value="companyItem.contactPhone"
                                @change="(e) => onCheckChange(e, companyItem, moduleIndex)"
                                :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                              </a-radio>
                            </a-radio-group>
                            <span v-if="companyItem.ecopartnerName" class="company_left company-name font-weight-500"
                              @click="toCompanyDetail(companyItem)">
                              <span class="company_underline">{{ companyItem.ecopartnerName }}</span>
                              <span
                                v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve && companyItem.approve != 1"
                                class="warning-text">生态厂商暂无该生态联系人！</span>
                              <span v-else-if="companyItem.auth == 0"
                                class="warning-text">该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！</span>
                              <span v-else-if="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve)"
                                class="warning-text">生态厂商暂无该生态联系人！</span>
                            </span>
                          </div>
                          <div class="company_right" style="display: inline-flex; align-items: center; ">
                            <img width="20px" height="20px" style="margin-bottom: 0;" src="@/assets/images/score.png" />
                            <span>生态评分</span>
                            <span class="score-font-class">{{ formatScore(companyItem) }}</span>
                          </div>
                          <a-select v-model:value="companyItem.contactName"
                            @change="(value) => selectUserCom(value, companyItem)"
                            :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)"
                            :getPopupContainer="triggerNode => triggerNode.closest('.re-select-eco-table-wrap')">
                            <template v-for="(opt, index) in companyItem.contactList" :key="index">
                              <a-select-option :value="opt.contactName"
                                :disabled="opt.approve != 1 || moduleItem.rejectCompanyIdlist.some((value) => value.userId == opt.userId)">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span
                            :style="{ color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999' }">
                            {{ companyItem.contactPhone }}
                          </span>
                        </div>
                        <div v-if="filterCompanyList(moduleItem, scope.row.company).length == 0">暂无数据</div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-table v-if="moduleItem.ecopartnerList?.[0]?.ownPerson?.length > 0"
                  :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                  <el-table-column v-if="!moduleItem.ecologyType" prop="ownPerson" label="自有能力方" />
                  <el-table-column v-if="moduleItem.ecologyType && moduleItem.ecologyType.includes('1')"
                    prop="ownPerson" label="自有能力方">
                    <template #default="scope">
                      <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box person-wrap">
                        <div style="display: flex;width:200px;">
                          <a-radio-group :value="moduleItem.selectIdOwn">
                            <a-radio :value="ownPersonItem.contactPhone"
                              @change="(e) => onCheckChange(e, ownPersonItem, moduleIndex)"
                              :disabled="moduleItem.rejectCompanyIdlist.some((value) => value.userId == ownPersonItem.userId)">
                            </a-radio>
                          </a-radio-group>
                          <span class="font-weight-500">
                            {{ ownPersonItem.belong }}
                          </span>
                        </div>
                        <span class="contactName">
                          {{ ownPersonItem.contactName }}
                        </span>
                        <p class="contactPhone">
                          {{ ownPersonItem.contactPhone }}
                        </p>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-table
                  v-if="moduleItem.status != 'reStar' && moduleItem.status != 'timeoutReject' && support_type == '省级' && !contralSupport && moduleItem.ecopartnerList?.[0]?.ownProvince?.length > 0"
                  :data="moduleItem.ecopartnerList" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                  <!-- reStar（重新发起），重新发起不显示方案/能力联系人 -->
                  <el-table-column prop="ownProvince" label="方案/能力联系人">
                    <template #default="scope">
                      <div v-for="(ownProvinceItem, index) in scope.row.ownProvince" :key="index"
                        class="box person-wrap">
                        <div style="display: flex;width:200px;">
                          <a-radio-group :value="moduleItem.selectIdOwn">
                            <a-radio :value="ownProvinceItem.belong"
                              @change="(e) => onCheckChange(e, ownProvinceItem, moduleIndex)"
                              :disabled="moduleItem.rejectCompanyIdlist.some((value) => value.userId == ownProvinceItem.userId)">
                            </a-radio>
                          </a-radio-group>
                          <span class="font-weight-500">
                            {{ ownProvinceItem.belong }}
                          </span>
                        </div>
                        <p class="contactName">
                          {{ ownProvinceItem.contactName }}
                        </p>
                        <p class="contactPhone">
                          {{ ownProvinceItem.contactPhone }}
                        </p>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="scrollable-table-wrap">
                <el-table v-if="moduleItem.status == 'selectApply'" :data="moduleItem.tableData1" :empty-text="'暂无数据'"
                  :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px;" class="scrollable-table">
                  <el-table-column prop="companyData" label="自有能力方/生态厂商" width="580">
                    <template #default="scope">
                      <span style="margin-right: 12px; display: inline-block">{{ scope.row.companyData.ecopartnerName
                      }}</span>
                      <span style="margin-right: 12px; display: inline-block">{{ scope.row.companyData.contactName
                      }}</span>
                      <span>{{ scope.row.companyData.contactPhone }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否支撑">
                    <template #default>
                      <a-button type="link" size="small" htmlType="button" @click="showApproveModal">同意</a-button>
                      <a-button type="link" danger size="small" htmlType="button" @click="showRejectModal">拒绝</a-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <p v-if="action != 'edit'" class="group_title weight500 font_14 font_00060e" style="margin-top:20px;">
                模块流程节点
                <span style="font-weight:normal;margin-left:6px;">{{ moduleItem.nodeName }}</span>
              </p>
              <p v-if="moduleItem.status != 'dealedAll' && moduleItem.status == 'reStar'"
                class="group_title weight500 font_14 font_00060e" style="margin-top:20px">驳回理由
                <span style="font-weight:normal;color:red;margin-left:6px;">{{ moduleItem.auditReason }}</span>
              </p>
              <p v-if="(moduleItem.status == 'timeoutReject' && moduleItem.isReassign == '0') || moduleItem.status == 'reStar'"
                class="group_title weight500 font_14 font_00060e" style="margin-top:20px;">调度管理员
                <a-select placeholder="请选择调度管理员" v-model:value="moduleItem.dispatchUser" allowClear
                  style="margin-left:6px;font-weight:normal;width:350px;" :getPopupContainer="getPopupContainer()">
                  <template v-for="opt in personList" :key="opt.id">
                    <a-select-option :value="String(opt.id)">
                      {{ formatRealName(opt.realName, opt.remark) }}
                    </a-select-option>
                  </template>
                </a-select>
              </p>
              <div v-if="moduleItem.status != 'selectApply' && moduleItem.status != 'dealedAll'"
                class="flex just-center" style="margin-top:10px;">
                <a-button v-if="moduleItem.status == 'toSelect' || moduleItem.status == 'reSelectPage'"
                  class="button_btn custom_btn reject_btn" @click="handleReject(moduleIndex)">驳回</a-button>
                <a-button
                  v-if="(action != 'edit' && moduleItem.status != 'reStar' && moduleItem.status != 'submitPage' && moduleItem.status != 'timeoutReject') || (moduleItem.status == 'timeoutReject' && moduleItem.isSupported == '1')"
                  class="button_btn custom_btn active_btn" @click="handleModuleSubmit(moduleIndex)"
                  :loading="addLoading">确定</a-button>
                <a-button v-if="(moduleItem.status == 'reStar' || moduleItem.status == 'submitPage')"
                  class="button_btn custom_btn cancel_btn" @click="endWork(moduleIndex)"
                  :loading="endWorkLoading">结束</a-button>
                <a-button v-if="(moduleItem.status == 'timeoutReject' && moduleItem.isReassign == '1')"
                  class="button_btn custom_btn cancel_btn" @click="confirmUnSupport(moduleIndex)">确认</a-button>
                <a-button v-if="(moduleItem.status == 'timeoutReject' && moduleItem.isReassign == '0')"
                  class="button_btn custom_btn cancel_btn" @click="reSubmit(moduleIndex)">发起工单</a-button>
                <a-button v-if="moduleItem.status == 'reStar'" class="button_btn custom_btn active_btn"
                  @click="reSubmit(moduleIndex)" :loading="addLoading">重新提交</a-button>
              </div>
            </div>
          </div>
          <div v-if="action == 'edit'" style="width: 100%;text-align: center;margin: 20px 0 0 0 ;">
            <a-button class="custom_btn active_btn" @click="addModule">新增支撑需求</a-button>
          </div>
          <a-row v-if="(action == 'edit' && !isDetail)" style="padding-left: 42px;margin-top:20px">
            <a-col :span="10">
              <a-form-item label="调度管理员" name="dispatchUser">
                <a-select placeholder="请选择调度管理员" v-model:value="formData.dispatchUser" allowClear
                  :getPopupContainer="getPopupContainer()">
                  <template v-for="opt in personList" :key="opt.id">
                    <a-select-option :value="String(opt.id)">
                      {{ formatRealName(opt.realName, opt.remark) }}
                    </a-select-option>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <SectionTitle v-if="action != 'edit' && isCreatedByMyself" custom-class="margin_t_24" title="流程备忘录" />
          <FlowMemo v-if="action != 'edit' && isCreatedByMyself" :order-id="Route.query.orderId"
            v-model:order-comments="orderComments" />
          <SectionTitle v-if="action != 'edit'" custom-class="margin_t_24" title="工单流程" />
          <div style="padding: 0 24px">
            <el-table v-if="action != 'edit'" :data="tableDataWork" class="resize-table-header-line"
              :empty-text="'暂无数据'" border :header-cell-style="{ textAlign: 'center' }"
              style="width: 100%; margin-top: 20px">
              <el-table-column align="center" prop="activityName" label="工单流程" width="180" />
              <el-table-column align="center" prop="assigneeName" label="处理人">
                <template #default="scope">
                  {{ scope.row.assigneeName }}
                  {{ scope.row.orgName ? '(' + scope.row.orgName + ')' : '' }}
                  {{ scope.row.phone }}
                </template>
              </el-table-column>
              <el-table-column align="left" prop="dealContent" label="处理内容">
                <template #default="scope">
                  <el-tooltip v-if="scope.row.dealContent && scope.row.dealContent.length > 60" trigger="hover"
                    popper-class="custom-tooltip" :content="scope.row.dealContent" placement="top">
                    <span v-if="scope.row.dealContent !== ''" class="content_control">
                      {{ scope.row.dealContent }}
                    </span>
                  </el-tooltip>
                  <span v-if="!scope.row.dealContent">-</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="endTime" label="处理时间" />
            </el-table>
          </div>
        </a-form>
      </div>
      <div class="flex just-center margin_t_32 margin_b_24">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="backout">返回</a-button>
        <a-button v-if="action == 'edit'" class="custom_btn active_btn" @click="createOrder"
          :loading="addLoading">确定</a-button>
      </div>
    </div>
    <EcoPartnerAddModal v-model:visible="showAdd" :display-options="displayOptions" :contact-list="contactList"
      :disabled-company-ids="formData.moduleForm[currentModuleIndex]?.companyIdList || []" :fetching="fetching"
      @ecology-change="ecologyChangeOld" @submit="submitAdd" />
    <SupportFeedbackModal v-model:visible="showSupportFeedbackModal" :is-reject-mode="isRejectMode"
      :is-third-eco="isThirdEco" :loading="confirmLoading" :initial-data="formDataDeal" @submit="handleOkSugget"
      @file-update="setFileDataDeal" @file-load="viewFileData" />
    <RejectTypeModal v-model:visible="showRejectTypeModal" :loading="confirmLoading" :initial-data="rejectForm"
      @submit="confirmRejectType" />
    <PostScoreModal v-model:visible="showPostScoreModal" :person-list="personListNew"
      :project-code-type="formData.projectCodeType" :initial-data="formDataScore" @submit="handleOkScore" />
    <RejectReasonModal v-model:visible="showRejectReasonModal" v-model:suggest="suggest" @ok="backLastComit"
      @close="closeRejectReasonModal" />
    <ModuleTypeModal v-model:visible="moduleTypeVisible"
      :title="'选择' + businessModuleTypeComputed(formData.moduleForm[currentModuleIndex]?.contentType)"
      :source-type="formData.moduleForm[currentModuleIndex]?.contentType"
      @select="(val) => handleSelectModule(val, currentModuleIndex)" />
    <ModifySupportTimeModal v-model:visible="showModifySupportTimeModal" :initial-data="modifySupportTimeForm"
      :disabled-date="disabledDate" @submit="confirmModifySupportTime" />
    <ConfirmSupportedModal v-model:visible="showConfirmSupportedModal" @confirm="confirmSupported"
      @cancel="cancelSupported" />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, onMounted, watch, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import 'moment/dist/locale/zh-cn';
import dayjs from "dayjs";
import FileUpload from "@/components/fileUpload/fileUpload.vue";
import FlowMemo from "./components/FlowMemo/index.vue";
import RejectReasonModal from "./components/RejectReasonModal/index.vue";
import EcoPartnerAddModal from "./components/AddModal/index.vue";
import SupportFeedbackModal from "./components/SupportFeedbackModal/index.vue";
import SupportInfoDescriptions from "./components/SupportInfoDescriptions/index.vue";
import RejectTypeModal from "./components/RejectTypeModal/index.vue";
import PostScoreModal from "./components/PostScoreModal/index.vue";
import ModuleTypeModal from "./components/ModuleTypeModal/index.vue";
import ModifySupportTimeModal from "./components/ModifySupportTimeModal/index.vue";
import ConfirmSupportedModal from "./components/ConfirmSupportedModal/index.vue";
import SectionTitle from "./components/SectionTitle/index.vue";
import BasicInfoForm from "./components/BasicInfoForm/index.vue";
import { batchSubmitWithdrawProcess, cityBack, midDispatch } from "@/api/processManage/backlog&completed/index.js";
import { getPriviligesData } from "@/api/processManage/permissionConfig/index.js";
import { QuestionFilled } from "@element-plus/icons-vue"; // 添加引入QuestionFilled图标
import {
  submitOrder,
  selectOrderById,
  operateOrderById,
  querytOrderById,
  selectOrderComments,
} from "@/api/processManage/index.js";
import { selectTree, selectTreeNew } from "@/api/system/team";
import {
  queryMultiDetail,
  getPerson,
  beginSend,
} from "@/api/ticket/ticket.js";
import { getAllUserList } from "@/api/system/user";
import { groupDataByMonthWithStats, sortTextList, processDuplicateCompaniesByName } from '@/utils/index.js';
export default defineComponent({
  components: {
    FileUpload,
    QuestionFilled,
    FlowMemo,
    RejectReasonModal,
    EcoPartnerAddModal,
    SupportFeedbackModal,
    SupportInfoDescriptions,
    RejectTypeModal,
    PostScoreModal,
    ModuleTypeModal,
    ModifySupportTimeModal,
    ConfirmSupportedModal,
    SectionTitle,
    BasicInfoForm,
  },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const getUserInfo = JSON.parse(window.localStorage.getItem("userInfo"));
    // 创建模块表单的默认结构
    const createDefaultModuleForm = (overrides = {}) => {
      return {
        companyIdList: [], // 生态合作方中auth并sync的的公司id的list
        contentType: "1", // 1方案2场景3能力
        dispatchUser: undefined, // 调度管理员id
        ecologyType: "",
        ecopartnerList: [], // 模块可选择生态方列表
        fileList: [],
        intro: "",
        ipartnerId: "",
        isBySelf: "2", // 1自己支撑2不自己支撑
        isReassign: "0", // 是否重新派单
        isSupported: "0", // 是否支撑
        name: "",
        needProvince: false, // 是否申请省级调度支撑
        procInstId: "",
        projectContent: "",
        provinceUser: "", // 省级调度员id
        reSelectData: [], // 厂商拒绝后调度人页面重新选择厂商数据
        rejectCompanyIdlist: [], // 厂商拒绝的id列表
        searchForm: {
          ecopartnerName: ""
        },
        searchState: {
          isApplied: false, // 是否已应用搜索
          cachedResults: null, // 缓存搜索结果
        },
        selectCompanyList: {},
        selectId: "",
        selectIdOwn: "",
        selectPhone: "",
        selectUsers: [],
        status: 'edit',
        tableData1: [],
        taskId: "",
        textList: [], // 生态厂商列表
        uid: null,
        ...overrides
      };
    };
    // 模块表单管理工具函数
    const moduleFormUtils = {
      // 添加新模块
      addModuleForms: (overrides = {}) => {
        data.formData.moduleForm.push(createDefaultModuleForm(overrides));
      },
      // 重置所有模块表单
      resetModuleForms: () => {
        data.formData.moduleForm = [createDefaultModuleForm()];
      },
      // 更新模块属性
      updateModuleForms: (index, updates) => {
        if (data.formData.moduleForm[index]) {
          Object.assign(data.formData.moduleForm[index], updates);
        }
      }
    };
    const data = reactive({
      queryModuleIdList: Route.query.idList ? JSON.parse(Route.query.idList) : [], // 从模块详情或一键调度带入的模块id和类型对象数组
      moduleTypeList: [{
        label: "基础方案",
        value: "1",
      }, {
        label: "场景方案",
        value: "2",
      }, {
        label: "原子能力",
        value: "3",
      }],
      isDetail: false, // 是否详情显示
      hasReStar: false, // 有无重新发起待办工单任务
      hasTimeoutReject: false, // 有无超时拒绝待办工单任务
      isCreatedByMyself: false,
      moduleTypeVisible: false,
      currentModuleIndex: 0,
      operateIndex: 0, // 待操作的模块索引
      title: "",
      orderComments: [],
      showPostScoreModal: false,
      showRejectReasonModal: false,
      suggest: "",
      formData: {
        projectName: undefined,
        title: "", // 工单标题
        projectCode: undefined,
        dispatchUser: undefined,
        userPhone: "",
        userInfo: "",
        supportMehod: "",
        time: "",
        projectCodeType: 1, // 1项目编码2商机编码
        moduleForm: [createDefaultModuleForm()]
      },
      projectOptions: [
        { name: '测试项目1', code: 'PROJ001' },
        { name: '测试项目2', code: 'PROJ002' },
        { name: '测试项目3', code: 'PROJ003' }
      ],
      personListNew: [],
      formDataScore: {
        isTurn: "2",
        projectName: undefined,
        projectCode: undefined,
        deliveryManager: "",
      },
      formDataDeal: {
        supportCos: "",
        rejectType: null,
        suggest: "",
        fileListDeal: [],
      },
      rejectForm: {
        suggest: "",
        rejectType: null,
      },
      isRejectMode: false,
      support_type: "",
      contralSupport: false, //true省直调度人
      userInfo: getUserInfo,
      ecopartnerId: undefined,
      enterpriseId: undefined,
      contactList: [],
      teamOldList: [],
      definitionId: "",
      action: Route.query.action,
      personList: [],
      provincePersonList: [],
      addLoading: false,
      endWorkLoading: false, // 结束按钮加载状态
      confirmLoading: false, // 处理意见弹框加载状态
      rules: {
        title: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        projectCodeType: [
          { required: true, message: "请选择信息来源" },
        ],
        projectName: [
          { required: true, message: "请选择DICT项目管理系统项目名称" },
        ],
        userInfo: [
          { required: true, message: "请输入需求发起人", trigger: "blur" },
        ],
        userPhone: [
          { required: true, message: "请输入需求发起人", trigger: "blur" },
        ],
        time: [{ required: true, message: "请选择支撑时限", trigger: "change" }],
        supportMehod: [
          { required: true, message: "请选择支撑方式", trigger: "change" },
        ],
        dispatchUser: [
          { required: true, message: "请选择调度管理员", trigger: "change" },
        ],
      },
      addRules: {
        company: [
          {
            required: true,
            message: "请选择生态合作方",
            trigger: "change",
          },
        ],
        contact: [
          {
            required: true,
            message: "请选择联系人",
            trigger: "change",
          },
        ],
      },
      viewLoading: false,
      formLoading: false,
      dealRules: {
        suggest: [
          {
            required: true,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ],
        supportCos: [
          {
            required: true,
            message: "请输入支撑人/天数",
            trigger: "change",
          },
        ],
        rejectType: [
          {
            required: true,
            message: "请选择拒绝理由类型",
            trigger: "change",
          },
        ],
      },
      tableDataWork: [],
      showAdd: false,
      companyId: "",
      isProvinceUser: false, //是否省级用户
      suggest: "", //处理意见
      showSupportFeedbackModal: false, //是否显示处理意见弹框
      showRejectTypeModal: false, //是否显示拒绝类型弹框
      isThirdEco: false, // 是否生态三方厂商
      cityPass: false, //市级通过省级退回市级
      showModifySupportTimeModal: false,// 修改支撑时间弹框
      modifySupportTimeForm: {
        originalSupportTime: null, // 用于弹窗显示修改前的时间
      },
      currentOperateModule: null,
      showConfirmSupportedModal: false,
      currentOperationStatus: null,
    });
    const mainFormRef = ref(null);
    const displayOptions = ref([]);
    const selectedValues = ref([]);
    const fetching = ref(false);
    const modifySupportTimeFormRef = ref(null);
    watch(() => data.formData.projectCodeType, (newVal) => {
      // 更新验证规则消息
      data.rules.projectName[0].message = newVal === 1 ? "请选择DICT项目管理系统项目名称" : "请输入DICT项目管理系统商机名称"
      // 清除该字段的验证状态
      if (mainFormRef.value) {
        mainFormRef.value.clearValidate('projectName')
      }
    })
    const getProcess = inject('getProcess'); //注入获取待办消息列表的函数
    const toChinese = (num) => {
      const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
      if (num <= 10) return chineseNumbers[num];
      if (num < 20) return `十${chineseNumbers[num - 10]}`;
      return num.toString();
    }
    const businessModuleTypeComputed = (type) => {
      const moduleType = data.moduleTypeList.find(item => item.value == type);
      return moduleType ? moduleType.label : '';
    };
    const createOrder = async () => {
      // 发起售前调度申请
      await mainFormRef.value?.validate().then(() => {
        data.addLoading = true;
        for (let index = 0; index < data.formData.moduleForm.length; index++) {
          const element = data.formData.moduleForm[index];
          if (!element.name) {
            data.addLoading = false;
            message.warning("请选择业务需求" + (index + 1));
            return
          }
        }
        const moduleList = data.formData.moduleForm.filter(item => (item.contentType && item.contentType == '1'))
        if (moduleList.length > 1) {
          message.warning("请最多只选择一个方案");
          data.addLoading = false;
          return;
        }
        for (let index = 0; index < data.formData.moduleForm.length; index++) {
          const element = data.formData.moduleForm[index];
          if (!element.needProvince && (!element.selectIdOwn && !element.selectPhone)) {
            message.warning("请选择生态厂商或方案/能力联系人");
            data.addLoading = false;
            return
          }
          if (element.needProvince && !element.provinceUser) {
            message.warning("请选择省级调度管理员");
            data.addLoading = false;
            return
          }
        }
        const temp = JSON.parse(JSON.stringify(data.formData));
        const { moduleForm, ...newObj } = temp;
        const postData = data.formData.moduleForm.map(module => {
          const { ecopartnerList, ...moduleFormWithoutEcopartner } = module;// 移除moduleForm的 ecopartnerList
          return {
            ...newObj,
            ...moduleFormWithoutEcopartner,
            company: ecopartnerList?.[0]?.company || [], // 生态厂商list
            ownPerson: ecopartnerList?.[0]?.ownPerson || [], // 自有能力方list
            ownProvince: ecopartnerList?.[0]?.ownProvince || [], // 方案/能力联系人list
            name: module.name, // 模块名称
            intro: module.intro, // 模块介绍
            companyIdList: module.companyIdList, // 生态合作方中auth并sync的的公司id的list
            dispatchUser: data.formData.dispatchUser, // 调度人id
            type: 8,
            time: data.formData.time, // 支撑时限
            contentType: module.contentType, // 模块类型
            linkId: module.uid, // 模块id
            businessType: 11, // 调度支撑
            isProvince: data.isProvinceUser, // 是否省级用户
            workTitle: data.formData.title, // 工单标题，关于xxx的售前支撑工单
            supportType: data.support_type, // 省级或市级
            selectPhone: module.selectPhone, // 自有能力方或生态厂商联系方式
            selectIdOwn: module.selectIdOwn, // 自有能力方联系方式
            ipartnerId: module.ipartnerId, // 合作方用户id
            selectCompanyList: module.selectCompanyList, // 合作方企业信息
            needProvince: false, // 是否申请省级调度支撑
            isBySelf: "2", // 1自己支撑2不自己支撑
          }
        });
        submitOrder({
          customDataForm: JSON.stringify(postData),
          type: 8,
          projectName: data.formData.projectName,
          projectCode: data.formData.projectCode,
          businessType: 11, // 调度支撑
          title: data.formData.title,
          nextUserId: data.formData.dispatchUser, // 调度人id
          isProvince: data.isProvinceUser, // 是否省级用户
          comment: "工单派发给调度管理员",
          procDefId: data.definitionId,
        }).then((res) => {
          message.success("提交成功");
          window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
        }).finally(() => {
          data.addLoading = false;
        });
      }).catch(() => { });
    }
    // 确定事件
    const handleModuleSubmit = async (index) => {
      data.operateIndex = index;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      try {
        data.addLoading = true;
        if (operateModule.status == "writeScore") {
          // 评分
          handleWriteScore(operateModule);
        } else if (operateModule.status === "reSelectPage") {
          // 重新选择能力方（市级重新调度/省直重新调度/省级重新调度）
          if (!operateModule.needProvince && operateModule.isBySelf != "1") {
            const supportTime = operateModule.time;
            if (!checkSupportTime(supportTime)) {
              data.modifySupportTimeForm.originalSupportTime = supportTime;
              data.currentOperateModule = operateModule;
              data.currentOperationStatus = 'reSelectPage'; // 保存操作状态
              data.showModifySupportTimeModal = true;
              data.addLoading = false;
              return;
            }
          }
          handleReSelectPage(operateModule);
        } else if (operateModule.status == "toSelect") {
          if (!operateModule.needProvince && operateModule.isBySelf != "1") {
            // 检查支撑时限是否至少3天
            const supportTime = operateModule.time;
            if (!checkSupportTime(supportTime)) {
              data.modifySupportTimeForm.originalSupportTime = supportTime;
              data.currentOperateModule = operateModule;
              data.currentOperationStatus = 'toSelect';
              data.showModifySupportTimeModal = true;
              data.addLoading = false;
              return;
            }
          }
          if (data.contralSupport) {
            // 省直调度
            handleProvincialSupport(operateModule);
          } else {
            // 市级/省级调度人调度
            // 检查是否所有模块都已选择生态厂商或方案/能力联系人
            handleCityAndProvince(operateModule);
          }
        } else if (operateModule.status == "timeoutReject") {
          // 超时拒绝-已支撑
          handleTimeoutReject(operateModule);
        } else {
          data.addLoading = false;
          console.log('other status')
        }
      } catch (error) {
        console.log(error);
        data.addLoading = false;
      };
    };
    const handleTimeoutReject = (operateModule) => {
      // 已支撑
      data.currentOperateModule = operateModule;
      data.showConfirmSupportedModal = true;
      data.addLoading = false; // 弹窗前先关闭加载
    };
    const handleCityAndProvince = (operateModule) => {
      if (!operateModule.selectIdOwn && !operateModule.selectPhone) {
        message.warning("请选择生态厂商或方案/能力联系人");
        data.addLoading = false;
        return;
      }
      let postData = []
      if (operateModule.needProvince) {
        // 需要省级支撑（市级调度）
        postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "申请省级调度支撑",
          nextUserIds: operateModule.provinceUser,
          variables: {
            needHelp: true,
            cityReject: false,
          }
        }];
      } else {
        // 不需要省级支撑（省级调度或市级调度）
        let companyNew = {
          module: operateModule.name,
          company: operateModule.selectCompanyList.ecopartnerName || operateModule.selectCompanyList.belong,
          contactPhone: operateModule.selectCompanyList.contactPhone,
          contactUserName: operateModule.selectCompanyList.contactName,
          contactUserJob: "",
          userId: operateModule.selectCompanyList.userId,
          enterpriseId: operateModule.selectCompanyList.enterpriseId,
          supportTime: operateModule.time,
        };
        if (data.support_type === "市级") {
          // 市级调度
          postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: "选择能力方",
            variables: {
              stfk: operateModule.ipartnerId || undefined,
              chooseType: "1",
              addCompanyInfo: JSON.stringify(companyNew),
              sendMsg: true,
              needHelp: false,
              cityReject: false,
              needProvinceSupport: false,
            }
          }];
        } else {
          // 省级调度
          if (operateModule.isBySelf == "1") {
            // 自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "调度管理员自己支撑",
              variables: {
                chooseType: "2",
                selfSupport: true,
                timeout: false,
              },
              chooseType: "2",
            }];
          } else {
            // 不自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "选择能力方",
              variables: {
                stfk: operateModule.ipartnerId || undefined,
                chooseType: "1",
                addCompanyInfo: JSON.stringify(companyNew),
                sendMsg: true,
              }
            }];
          }
        }
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handleProvincialSupport = (operateModule) => {
      let postData = []
      if (!operateModule.needProvince) {
        // 不需要省级支撑
        let companyNew = {
          module: operateModule.name,
          company: operateModule.selectCompanyList.ecopartnerName || operateModule.selectCompanyList.belong,
          contactPhone: operateModule.selectCompanyList.contactPhone,
          contactUserName: operateModule.selectCompanyList.contactName,
          contactUserJob: "",
          userId: operateModule.selectCompanyList.userId,
          enterpriseId: operateModule.selectCompanyList.enterpriseId,
          supportTime: operateModule.time,
        };
        postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "选择能力方",
          variables: {
            addCompanyInfo: JSON.stringify(companyNew),
            stfk: operateModule.selectCompanyList.userId,
            sendMsg: true,
            needProvinceSupport: false,
            chooseType: "1",
            feedbackReject: "3",
          },
        }];
      } else {
        // 需要省级支撑
        if (!operateModule.provinceUser) {
          message.warning("请选择省级调度管理员");
          data.addLoading = false;
          return
        }
        postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "申请省级调度支撑",
          nextUserIds: operateModule.provinceUser,
          variables: {
            needProvinceSupport: true,
            chooseType: "1",
          },
        }];
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handlExistTask = () => {
      operateOrderById(Route.query.orderId).then((res) => {
        let tempProInstList = res.data.procInstList;
        if (tempProInstList && tempProInstList.length > 0) {
          // 该工单仍有待办，操作完成
          actionInitData();
        } else {
          getProcess(); // 重新获取待办工单数据
          window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
        }
      }).finally(() => {
        data.addLoading = false;
        data.endWorkLoading = false;
      });
    }
    const actionInitData = () => {
      moduleFormUtils.resetModuleForms();
      getData();
    }
    const handleReSelectPage = (operateModule) => {
      let company = {
        company: operateModule.selectCompanyList.ecopartnerName || operateModule.selectCompanyList.belong,
        contactPhone: operateModule.selectCompanyList.contactPhone,
        contactUserName: operateModule.selectCompanyList.contactName,
        contactUserJob: "",
        userId: operateModule.selectCompanyList.userId,
        enterpriseId: operateModule.selectCompanyList.enterpriseId,
      };
      // 添加支撑时间到 company 对象
      company.supportTime = operateModule.time;
      // 重新调度
      if (!operateModule.needProvince && !company.userId && operateModule.isBySelf == '2') {
        message.warning("请选择生态厂商或方案/能力联系人");
        data.addLoading = false;
        return
      }
      if (operateModule.needProvince && !operateModule.provinceUser) {
        message.warning("请选择省级调度管理员");
        data.addLoading = false;
        return
      }
      // 检查能力方是否已拒绝
      const isRejected = operateModule.rejectCompanyIdlist.some(
        value => value.userId === operateModule.selectCompanyList.userId
      );
      if (isRejected) {
        const msg = operateModule.selectCompanyList.enterpriseId ? "该厂商联系人已拒绝，请重新选择" : "该自有能力方已拒绝，请重新选择";
        message.warning(msg);
        data.addLoading = false;
        return
      }
      if (!operateModule.dispatchUser) {
        message.warning("请选择调度管理员");
        data.addLoading = false;
        return
      }
      let postData = [];
      if (data.support_type === "市级") {
        // 市级重新调度
        if (operateModule.needProvince) {
          // 申请省级支撑
          postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: "申请省级调度支撑",
            nextUserIds: operateModule.provinceUser,
            variables: {
              needHelp: true,
              cityReject: false,
            }
          }];
        } else {
          // 不申请省级支撑
          postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: "重新选择能力方",
            variables: {
              addCompanyInfo: JSON.stringify({ ...company, module: operateModule.name }),
              stfk: company.userId,
              chooseType: "1",
              sendMsg: true,
              needHelp: false,
              cityReject: false,
              needProvinceSupport: false,
            }
          }];
        }
      } else {
        // 省级/省直重新调度
        if (data.contralSupport) {
          if (operateModule.needProvince) {
            // 需要升级支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "申请省级调度支撑",
              nextUserIds: operateModule.provinceUser,
              variables: {
                needProvinceSupport: true,
                chooseType: "1",
              },
            }];
          } else {
            // 不需要省级支撑，省直重新调度
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "重新选择能力方",
              variables: {
                addCompanyInfo: JSON.stringify(company),
                stfk: company.userId,
                chooseType: "1",
                sendMsg: true,
                needProvinceSupport: false,
              },
            }];
          }
        } else {
          // 省级重新调度
          if (operateModule.isBySelf === "1") {
            // 自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "调度管理员自己支撑",
              variables: {
                chooseType: "2",
                selfSupport: true,
              },
              chooseType: "2"
            }];
          } else {
            // 不自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "重新选择能力方",
              variables: {
                addCompanyInfo: JSON.stringify({ ...company, module: operateModule.name }),
                stfk: company.userId,
                chooseType: "1",
                sendMsg: true,
              }
            }];
          }
        }
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handleWriteScore = (operateModule) => {
      let tempTextList = operateModule.textList.filter((i) => i.enterpriseId);
      tempTextList = tempTextList.filter((i) => i.dealType && (i.dealType == "1" || (i.dealType == '3' && i.hasSupported)));// 同意的才可以评价
      const hasEmptyScore = tempTextList.some((i) => !i.scored && (!i.satisfiedScore || !i.responseScore));// 没有评过分，评分为空
      if (hasEmptyScore) {
        // 自有能力方、方案/能力联系人不支持生态评分，生态厂商需要评分
        message.warning("评分不能为空");
        data.addLoading = false;
        return false;
      }
      querytOrderById(Route.query.orderId).then((res) => {
        const taskList = res.data.todoTasks || [];
        const procCount = res.data.procCount;
        if (procCount == 1) {
          // 单模块直接转售中
          data.formDataScore.projectCode = data.formData.projectCode;
          data.formDataScore.projectName = data.formData.projectName;
          data.formDataScore.deliveryManager = undefined;
          data.formDataScore.isTurn = "2";
          data.showPostScoreModal = true;
          data.addLoading = false;
          return
        }
        if (taskList.length > 1) {
          // 不是最后一个打分的模块，只评分
          // 只提交同意的且没有评过分的生态方数据
          handleOrderTask();
        } else {
          // 最后一个打分的模块，弹售中弹窗
          if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId) {
            data.formDataScore.projectCode = data.formData.projectCode;
            data.formDataScore.projectName = data.formData.projectName;
            data.formDataScore.deliveryManager = undefined;
            data.formDataScore.isTurn = "2";
            data.showPostScoreModal = true;
            data.addLoading = false;
          } else {
            handleOrderTask();
          }
        }
      }).catch(() => {
        data.addLoading = false;
      })
    };
    const handleOrderTask = () => {
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const ownPerson = operateModule.textList.filter((item) => !item.enterpriseId && (!item.scored && (item.dealType == "1" || (item.dealType == '3' && item.hasSupported)))); // enterpriseId为null是方案/能力联系人或自有能力方，不需要打分，需要生态评价
      const thirdPerson = operateModule.textList.filter((item) => item.enterpriseId && (!item.scored && (item.dealType == "1" || (item.dealType == '3' && item.hasSupported)))); // enterpriseId非null是生态厂商，需要打分，需要生态评价
      // 移除allParseData
      const filteredOwnPerson = ownPerson.map(({ allParseData, ...rest }) => rest);
      const filteredThirdPerson = thirdPerson.map(({ allParseData, ...rest }) => rest);
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: "评分完毕",
        variables: {
          addScoreInfo: JSON.stringify({ writeScore: ownPerson.length > 0 ? filteredOwnPerson : filteredThirdPerson }),
        },
      }];
      if (postData.length == 0) {
        message.warning("请至少选择一个生态厂商进行评分");
        data.addLoading = false;
        return;
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success("评分成功");
        handlExistTask();
      });
    };
    const backout = () => {
      data.addLoading = false;
      Router.go(-1);
    };
    // 格式化生态合作方list
    const formatEcopartnerList = async (index, { ecopartnerList = [], ecologyType, contact, createBy, phone, provider, name }) => {
      ecopartnerList = ecopartnerList || [];
      // 设置生态类型
      data.formData.moduleForm[index].ecologyType = ecologyType;
      let tempCompany = []; // 生态厂商
      let tempOwnPerson = []; // 自有能力方
      let tempOwnProvince = []; // 方案/能力联系人
      // 根据生态类型过滤数据
      if (ecologyType) {
        if (ecologyType.includes("2")) {
          // 生态厂商
          tempCompany = ecopartnerList.filter(item => item.ecopartnerId !== null);
        }
        if (ecologyType.includes("1")) {
          // 自有能力方
          tempOwnPerson = ecopartnerList.filter(item => item.ecopartnerId == null);
          // 批量处理自有能力方信息
          const promises = tempOwnPerson.map(async (person) => {
            if (person.contactPhone) {
              person.belong = await handleEcopartnerInfo(person.contactPhone);
            }
          });
          await Promise.all(promises);
        }
      }
      // 筛选授权且同步的企业ID
      data.formData.moduleForm[index].companyIdList = tempCompany.filter(item => item.auth === 1 && item.sync === 1).map(item => item.enterpriseId);
      // 批量处理联系人列表 - 使用Promise.all优化异步处理
      const contactPromises = tempCompany
        .filter(item => item.ecopartnerName) // 只处理有名称的项
        .map(async (item) => {
          try {
            const res = await selectTreeNew(item.ecopartnerName);
            item.contactList = res.data?.[0]?.contactList || [];
          } catch (error) {
            console.error(`获取联系人列表失败: ${item.ecopartnerName}`, error);
            item.contactList = [];
          }
        });
      // 等待所有联系人信息获取完成
      await Promise.all(contactPromises);
      // 解析provider信息
      const providerParts = provider ? provider.split("/") : [];
      const belongDepartment = providerParts[2] || '';
      // 构建模块方案/能力联系人信息
     tempOwnProvince = [{
        contactName: contact, // 模块联系人
        userId: createBy, // 模块创建人id
        contactPhone: phone, // 模块联系方式
        belong: belongDepartment, // 模块归属部门
      }];
      // 构建售前调度流程表格数据
      data.formData.moduleForm[index].ecopartnerList = [{
        name: name, // 模块名称
        company: tempCompany, // 生态厂商
        ownPerson: tempOwnPerson, // 自有能力方
        ownProvince: tempOwnProvince, // 方案/能力联系人
      }];
    };
    const getData = () => {
      data.formData.userPhone = data.userInfo.phone;// 发起人联系方式
      data.formData.userInfo = data.userInfo.realName.charAt(0) + "经理";// 需求发起人
      let params = {
        pageNum: 1,
        pageSize: 10,
        processName: "售前调度工单",
      };
      beginSend(params).then((res) => {
        // 获取售前调度工单流程id
        data.definitionId = res.data ? res.data.rows[0].definitionId : "";
      });
      if (
        Route.query.type == "dealedAll" ||// 查看详情
        Route.query.type == "selectApply" || // 合作方处理同意\拒绝操作
        Route.query.type == "writeScore" || // 发起人评分
        Route.query.type == "toSelect" || // 省级调度人选择合作方
        Route.query.type == "toSelectFirst" || // 省直调度管理员选择合作方
        Route.query.type == "multiModule" // 多模块
      ) {
        data.formLoading = true;
        getAllUserList({ roleId: 71, scope: 0 }).then((res) => {
          // 转售中时的交付经理列表
          data.personListNew = res.data;
        });
        const apiCall = Route.query.type === "dealedAll" ? selectOrderById : operateOrderById;
        apiCall(Route.query.orderId).then((res) => {
          data.formData = { ...data.formData, ...res.data };
          let tempProInstList = res.data.procInstList;
          data.isCreatedByMyself = res.data.createBy == data.userInfo.id; // 是否自己创建的工单
          if (data.action != 'edit' && data.isCreatedByMyself) {
            // 如果是自己创建的工单，查询工单评论
            queryOrderComments(Route.query.orderId);
          }
          if (tempProInstList && tempProInstList.length > 0) {
            const tempParsedData = JSON.parse(JSON.parse(tempProInstList[0].customDataForm));
            data.formData = { ...data.formData, ...tempParsedData }; // 填充工单信息和基础信息
            for (let index = 0; index < tempProInstList.length; index++) {
              // 遍历并回填模块及生态方信息
              const element = tempProInstList[index];
              const parsedData = JSON.parse(JSON.parse(element.customDataForm));
              if (!data.formData.moduleForm[index]) {
                moduleFormUtils.addModuleForms();
              }
              data.cityPass = parsedData.cityPass;// 省级才返回该值
              data.formData.moduleForm[index] = { ...data.formData.moduleForm[index], ...parsedData };
              if (Route.query.type == "multiModule") {
                const taskName = element?.tasks?.[0]?.taskName;
                if (taskName == "调度中" || taskName == "地市调度人" || taskName == "省级调度人") {
                  data.formData.moduleForm[index].status = "toSelect";
                }
                if (taskName == "省直调度管理员") {
                  data.formData.moduleForm[index].status = "toSelectFirst";
                }
                if (taskName == "生态评分") {
                  data.formData.moduleForm[index].status = "writeScore";
                }
                if (taskName == "提交申请") {
                  if (element?.tasks?.[0].procVars.selfSupport) {
                    data.formData.moduleForm[index].status = "submitPage";
                  } else {
                    const preSaleDispatchFeedbackInfos = element?.preSaleDispatchFeedbackInfos || [];
                    if (preSaleDispatchFeedbackInfos?.[preSaleDispatchFeedbackInfos.length - 1]?.dealType == "3") {
                      data.formData.moduleForm[index].status = "timeoutReject";
                    } else {
                      data.formData.moduleForm[index].status = "reStar";
                    }
                    data.formData.moduleForm[index].dispatchUser = undefined; // 重新提交调度人id设置为空，申请省级时dispatchUser会变为省级调度人的id，所以要设置空
                  }
                }
                if (taskName == "生态反馈" || taskName == "能力方反馈") {
                  data.formData.moduleForm[index].status = "selectApply";
                }
              } else if (Route.query.type == "dealedAll") {
                data.formData.moduleForm[index].status = "dealedAll";
              } else {
                data.action = Route.query.type
              }
              // 设置模块类型,转换为字符串类型
              data.formData.moduleForm[index].contentType = String(parsedData.contentType);
              data.formData.moduleForm[index].taskId = element?.tasks?.[0]?.taskId || element?.tasks?.[0]?.id || "";
              data.formData.moduleForm[index].dispatcherChanged = element?.tasks?.[0]?.procVars.dispatcherChanged || false; // 支撑方是否变更
              data.formData.moduleForm[index].procInstId = element.procInstId;
              data.formData.moduleForm[index].selectCompanyList = parsedData.selectCompanyList || {};
              data.formData.moduleForm[index].isSupported = "0"; // 1支撑2不支撑
              data.formData.moduleForm[index].isReassign = '0';
              data.formData.moduleForm[index].ecopartnerList = (parsedData.company?.length > 0 && parsedData.company[0].name) ? parsedData.company : [{
                name: parsedData.name,
                company: parsedData.company,
                ownPerson: parsedData.ownPerson || [],
                ownProvince: parsedData.ownProvince || [],
              }];
              data.formData.moduleForm[index].projectContent = parsedData.projectContent;
              data.formData.moduleForm[index].fileList = parsedData.fileList || [];
              data.formData.moduleForm[index].nodeName = element.historyProcNodeList[0].activityName;
              if (data.formData.moduleForm[index].status == 'reStar') {
                actioAuditReason(element, index);
              }
              queryEcoparterComment(element?.preSaleDispatchFeedbackInfos, index, parsedData);
            }
            data.tableDataWork = res.data.historyProcNodeList.reverse();
            if (data.tableDataWork[data.tableDataWork.length - 1].activityName == "已审核") {
              data.tableDataWork = data.tableDataWork.slice(0, -1);
            }
            if (data.tableDataWork[data.tableDataWork.length - 1].activityName == "结束") {
              data.tableDataWork = data.tableDataWork.slice(0, -1);
            }
            data.tableDataWork.forEach((item) => {
              if (item.commentList && item.commentList.length > 0) {
                item.dealContent = item.commentList[0].fullMessage;
                if (item.activityName == "调度人确认" && item.commentList[0].message == "") {
                  item.dealContent = "请发起人评分";
                }
                if (item.activityName == "能力方反馈") {
                  if (item.commentList[0].type == "2") {
                    item.dealContent = "驳回。" + item.commentList[0].message;
                  }
                }
                if (item.activityName == "生态反馈" || item.activityName == "能力方反馈") {
                  if (item.commentList[0].type == 1) {
                    item.dealContent = "同意。" + item.commentList[0].message;
                  } else if (item.commentList[0].type == 2) {
                    item.dealContent = "拒绝。" + item.commentList[0].message;
                  }
                }
              } else {
                item.dealContent = "";
              }
              if (item.endTime == null) {
                item.endTime = "-";
              }
            });
          }
          data.isDetail = data.formData.moduleForm.some(item => item.status == 'dealedAll')
        }).finally(() => {
          data.formLoading = false;
        });

        data.formData.moduleForm.map(item => {
          if (item.status == "toSelectFirst") {
            // 调度人选择合作方
            data.contralSupport = true; // 是省直调度人
            item.status = "toSelect";
            const existReject = data.formData.moduleForm[data.currentModuleIndex].textList.some((item) => item.dealType == "2");
            if (existReject) {
              // 有拒绝的反馈
              item.status = "reSelectPage";
            }
          }
        });
      } else if (Route.query.idList) {
        // 从模块详情和一键调度发起的申请支撑
        // 批量查询多个模块
        const idList = JSON.parse(Route.query.idList);
        queryMultiDetail({ data: idList }).then(async (res) => {
          // 遍历返回的模块数据
          let moduleIndex = 0;
          Object.entries(res.data).forEach(([type, modules]) => {
            modules.forEach((moduleData, index) => {
              if (!data.formData.moduleForm[moduleIndex]) {
                // 如果对应索引没有模块表单，创建一个新的
                moduleFormUtils.addModuleForms({
                  contentType: type, // 1方案2场景3能力
                  intro: moduleData.description || moduleData.summary || moduleData.abilityIntro || "",
                  name: moduleData.name,
                  uid: moduleData.id,
                });
              } else {
                // 如果已经存在，更新内容类型和其他信息
                moduleFormUtils.updateModuleForms(moduleIndex, {
                  contentType: type,
                  intro: moduleData.description || moduleData.summary || moduleData.abilityIntro || "",
                  name: moduleData.name,
                  uid: moduleData.id, // 能力id
                });
              }
              // 格式化生态合作方列表
              formatEcopartnerList(moduleIndex, moduleData);
              moduleIndex++;
            });
          });
          // 设置工单标题
          formatOrderTitle();
        });
      } else if (Route.query.from == "center") {
        // 从调度中心工作台发起的申请支撑
      }
      getPersonList();
    };
    const actioAuditReason = ({ historyProcNodeList, preSaleDispatchFeedbackInfos }, index) => {
      let tableDataWork = JSON.parse(JSON.stringify(historyProcNodeList));
      if (tableDataWork[tableDataWork.length - 1].activityName === "已审核") {
        tableDataWork = tableDataWork.slice(1, -1);
      } else {
        tableDataWork = tableDataWork.slice(1);
      }
      if (tableDataWork[tableDataWork.length - 1].activityName === "结束") {
        tableDataWork = tableDataWork.slice(0, -1);
      }
      const distDataList = tableDataWork
        .reverse()
        .slice(1)
        .map((item) => {
          const result = {
            activityName: item.activityName,
            assigneeName: item.assigneeName,
            phone: item.phone,
            dealContent: "",
            endTime: item.endTime || "-",
          };
          if (item.commentList?.length > 0) {
            const comment = item.commentList[0];
            result.dealContent = comment.fullMessage;
            if (item.activityName == "能力方反馈") {
              if (item.commentList[0].type == "2") {
                item.dealContent = "驳回。" + item.commentList[0].message;
              }
            }
          }
          return result;
        });
      data.formData.moduleForm[index].auditReason = distDataList[distDataList.length - 1]?.dealContent || preSaleDispatchFeedbackInfos[preSaleDispatchFeedbackInfos.length - 1]?.rejectReason || "";
    };
    const queryOrderComments = (orderId) => {
      selectOrderComments(orderId).then((res) => {
        data.orderComments = groupDataByMonthWithStats(res.data);
      });
    };
    // 生态厂商反馈
    const queryEcoparterComment = (dataList, moduleIndex, parsedData) => {
      const operateModule = data.formData.moduleForm[moduleIndex];
      if (!parsedData.fileListDeal) parsedData.fileListDeal = []
      // 获取生态厂商反馈信息列表，allData为每个厂商的反馈记录（同意/拒绝/处理中等）
      let allData = dataList;
      const newTextList = allData.map((item) => {
        return {
          ...item,
          suggest: "", // 增加生态评价字段
          module: parsedData.name,// 解决后台返回生态方第二个缺少模块名的问题
          allParseData: parsedData,
        };
      });
      let num = allData.length;
      data.formData.moduleForm[moduleIndex].textList = JSON.parse(JSON.stringify(newTextList)); // 用于页面展示所有生态厂商反馈信息
      if (operateModule.status == "toSelectFirst") {
        // 如果是省直调度人首次选择合作方，判断是否有反馈信息，决定页面状态
        data.contralSupport = true;// 标记为省直调度人
        operateModule.status = "toSelect";
        const existReject = data.formData.moduleForm[moduleIndex].textList.some((item) => item.dealType == "2");
        if (existReject) {
          // 有拒绝的反馈
          operateModule.status = "reSelectPage";
        }
      }
      // 遍历所有反馈，收集被拒绝的厂商信息，便于后续过滤和重新选择
      allData.forEach((item) => {
        if (item.dealType == "2" || (item.dealType == "3" && !item.hasSupported)) { // 2为拒绝 3为超时拒绝
          // 构造被拒绝厂商信息，加入拒绝列表
          let tempInfo = {
            ecopartnerName: item.company,
            contactName: item.contactUserName,
            contactPhone: item.contactPhone,
            userId: item.userId,
            enterpriseId: item.enterpriseId,
          };
          data.formData.moduleForm[moduleIndex].rejectCompanyIdlist.push(tempInfo);
          // 构造可重新选择能力方的数据结构
          // let ar = [
          //   {
          //     name: data.formData.moduleForm[moduleIndex].ecopartnerList[0].name,
          //     company: data.formData.moduleForm[moduleIndex].ecopartnerList[0].company,
          //     ownPerson: data.formData.moduleForm[moduleIndex].ecopartnerList[0].ownPerson,
          //     ownProvince: data.formData.moduleForm[moduleIndex].ecopartnerList[0].ownProvince,
          //   },
          // ];
          // data.formData.moduleForm[moduleIndex].ecopartnerList = ar;
          // 过滤掉已被拒绝的联系人，优先选可用联系人
          // data.formData.moduleForm[moduleIndex].ecopartnerList[0].company.forEach((item) => {
          //   if (item.contactList) {
          //     const availableContact = item.contactList.find((contact) =>
          //       contact.approve === 1 &&
          //       !data.formData.moduleForm[moduleIndex].rejectCompanyIdlist.some((value) => value.userId == contact.userId)
          //     );
          //     if (availableContact) {
          //       item.contactName = availableContact.contactName;
          //       item.contactPhone = availableContact.contactPhone;
          //       item.userId = availableContact.userId;
          //     }
          //   }
          // });
        }
      });
      // 判断最后一个反馈是否为拒绝，若是则进入重新选择能力方页面
      if (allData[num - 1] && allData[num - 1].dealType == "2") {
        if (operateModule.status == "toSelect") {
          operateModule.status = "reSelectPage";
        }
      }
      data.formData.moduleForm[moduleIndex].reSelectData = allData; // // 展示所有反馈信息
      data.formData.moduleForm[moduleIndex].reSelectData.forEach((item) => {
        // 构造表格展示用的info字段
        item.info = {
          ecopartnerName: item.company,
          contactName: item.contactUserName,
          contactPhone: item.contactPhone,
        };
        item.dealTime = item.dealTime;
        item.rejectReason = item.dealContent || '-';
        if (item.dealType == "2") {
          item.dealContent = "拒绝"; // 标记为拒绝
        } else if (item.dealType == "1") {
          item.dealContent = "同意";
        } else if (item.dealType == "3") {
          item.dealContent = "超时拒绝";
        }
      });
      // 初始化评分表，仅对未处理的厂商（dealType==0或null）赋默认分数
      allData.forEach((item) => {
        if (item.dealType == "0" || item.dealType == null) {
          data.formData.moduleForm[moduleIndex].tableData1.push({
            companyData: {
              ecopartnerName: item.company,
              contactName: item.contactUserName,
              contactPhone: item.contactPhone,
            },
            satisfiedScore: 1, // 默认满意度分数
            responseScore: 1, // 默认响应分数
            module: data.formData.moduleForm[moduleIndex].name,
          });
        }
      });
      if (Route.query.type == "writeScore") {
        for (let i = 0; i < data.formData.moduleForm.length; i++) {
          // 评分页面去重处理:同一公司多条反馈时，优先保留非拒绝项
          data.formData.moduleForm[i].textList = processDuplicateCompaniesByName(data.formData.moduleForm[i].textList);
          // 同意的排第一，否则按时间最新拒绝的排序
          data.formData.moduleForm[i].textList = sortTextList(data.formData.moduleForm[i].textList)
        }
      }
    };
    // 获取当前用户所属省级还是市级
    const getPersonList = () => {
      let roleKey = "";
      let org = data.userInfo.orgId;
      // let roleId = data.userInfo.roleIds;
      if (
        org == "2" ||
        org == "3" ||
        org == "4" ||
        org == "5" ||
        org == "6" ||
        org == "7" ||
        org == "8" ||
        org == "9" ||
        org == "10" ||
        org == "11" ||
        org == "12" ||
        org == "13" ||
        org == "14"
      ) {
        roleKey = "cityIndustryCharge";
        data.support_type = "市级";
        data.isProvinceUser = false;
      } else {
        roleKey = "provinceDispatchAdmin";
        data.isProvinceUser = true;
        data.support_type = "省级";
      }
      if (roleKey == "cityIndustryCharge") {
        getAllUserList({ roleKey }).then(res1 => {
          let personList = res1.data || [];

          return getPriviligesData({ roleIds: 72, pageSize: 100 }).then(res2 => {
            const priviligesList = res2.data.rows || [];

            priviligesList.forEach(pUser => {
              const existUser = personList.find(u => u.username === pUser.username);
              if (existUser) {
                if (pUser.remark && pUser.remark.trim() !== "") {
                  existUser.remark = pUser.remark.trim();
                }
              } else {
                personList.push(pUser);
              }
            });

            data.personList = personList;
          });
        });
      } else {
        getAllUserList({ roleKey, scope: 0 }).then((res) => {
          const promises = [Promise.resolve(res)];
          Promise.all(promises).then((results) => {
            data.personList = results.flatMap((result) => result.data);
          });
        });
      }
    };
    getData();
    // 厂商拒绝支撑
    const refuseApply = (formData) => {
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: formData.suggest,
        variables: {
          chooseType: "2",
          rejectType: Number(formData.rejectType),
        },
        chooseType: "2",
      }];
      // 如果支撑方已变更,流程到调度人,否则流程到发起人
      if (operateModule.dispatcherChanged) {
        postData[0].nextUserIds = operateModule.dispatchUser;
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        if (res.code == 200) {
          data.showSupportFeedbackModal = false;
          handlExistTask();
        }
      }).finally(() => {
        data.confirmLoading = false;
      });
    };
    // 驳回
    const handleReject = (index) => {
      data.showRejectReasonModal = true; // 显示驳回原因输入框
      data.operateIndex = index;
    };
    // 调度人驳回
    const backLastComit = () => {
      if (data.suggest == "") {
        message.error("请填写驳回原因！");
        return;
      }
      if (data.support_type == "市级") {
        // 市级调度人驳回
        const operateModule = data.formData.moduleForm[data.operateIndex];
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: data.suggest,
          variables: {
            chooseType: "2",
            cityReject: true,
            needHelp: false,
          },
          chooseType: "2",
        }];
        batchSubmitWithdrawProcess(postData).then((res) => {
          message.success(res.msg);
          data.suggest = "";
          data.showRejectReasonModal = false
          handlExistTask();
        });
      } else {
        // 省级调度人驳回
        if (data.cityPass) {
          // 省级退回到市级调度人
          const operateModule = data.formData.moduleForm[data.operateIndex];
          const postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: data.suggest,
            nextUserIds: operateModule.dispatchUser,
            variables: {
              chooseType: "2",
              selfSupport: false,
            },
            chooseType: "2",
          }];
          batchSubmitWithdrawProcess(postData).then((res) => {
            message.success(res.msg);
            data.suggest = "";
            data.showRejectReasonModal = false
            handlExistTask();
          });
        } else if (data.contralSupport) {
          const operateModule = data.formData.moduleForm[data.operateIndex];
          if ((operateModule.status == "toSelect" || operateModule.status == "reSelectPage")) {
            // 省直调度时驳回（省直驳回到申请人）或省直重新调度时
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: data.suggest,
              variables: {
                chooseType: "2",
                needProvinceSupport: false,
              },
              chooseType: "2",
            }];
            batchSubmitWithdrawProcess(postData).then((res) => {
              message.success(res.msg);
              data.suggest = "";
              data.showRejectReasonModal = false
              handlExistTask();
            });
          }
        } else {
          // 省级驳回到省直
          const operateModule = data.formData.moduleForm[data.operateIndex];
          const postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: data.suggest,
            nextUserIds: operateModule.dispatchUser, // 省直申请省级支撑后dispatchUser改为省直自己，所以dispatchUser是省直用户id
            variables: {
              chooseType: "2",
              selfSupport: false,
            },
            chooseType: "2",
          }];
          batchSubmitWithdrawProcess(postData).then((res) => {
            message.success(res.msg);
            data.suggest = "";
            data.showRejectReasonModal = false
            handlExistTask();
          });
        }
      }
    };
    const addCooperate = (value, index) => {
      data.companyId = value.name;
      data.showAdd = true;
      data.formData.moduleForm[index].companyIdList = value.company.map(item => { return item.enterpriseId });
      data.currentModuleIndex = index;
    };
    const ecologyChangeOld = (val) => {
      if (val) {
        let list = data.teamOldList.filter((item) => {
          return item.name == val;
        });
        data.contactList = list[0].contactList;
      } else {
        data.contactList = [];
      }
    };
    // 新增生态厂商时的搜索函数
    // const handleSearch = debounce((val) => {
    //   fetching.value = true;
    //   selectTree({ name: val }).then((res) => {
    //     fetching.value = false;
    //     const mockData = res.data.filter((item) => {
    //       return item.id != "1189";
    //     });
    //     data.teamOldList = mockData;
    //     displayOptions.value = mockData;
    //   });
    // }, 600);
    function debounce(fn, delay) {
      let time = null;
      return function () {
        let context = this;
        let args = arguments;
        if (time) {
          clearTimeout(time);
        }
        time = setTimeout(() => {
          // fn.call(this)
          fn.apply(context, args);
        }, 600);
      };
    }
    const selectUserCom = (value, item) => {
      item.contactName = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).contactName;
      const selectedCompany = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      );
      if (selectedCompany) {
        item.contactPhone = selectedCompany.contactPhone;
      }
      item.userId = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).userId;
    };
    // 新增生态厂商
    const submitAdd = (value) => {
      try {
        if (data.formData.moduleForm[data.currentModuleIndex].status == "reSelectPage") {
          let com = {
            ecopartnerName: value.company,
            contactPhone: value.phone,
            contactName: value.contact,
            userId: value.userId,
            contactList: data.contactList,
            enterpriseId: value.enterpriseId,
            sync: value.sync,
            auth: value.auth,
            approve: value.approve,
            totalScore: value.totalScore,
            introScore: value.introScore,
          };
          data.formData.moduleForm[data.currentModuleIndex].ecopartnerList[0].company.unshift(com);
        } else if (data.action == "edit") {
          let com = {
            ecopartnerName: value.company,
            contactPhone: value.phone,
            contactName: value.contact,
            userId: value.userId,
            contactList: data.contactList,
            enterpriseId: value.enterpriseId,
            sync: value.sync,
            auth: value.auth,
            approve: value.approve,
            totalScore: value.totalScore,
            introScore: value.introScore,
          };
          data.formData.moduleForm[data.currentModuleIndex].ecopartnerList[0].company.unshift(com);
        } else {
          data.formData.moduleForm[data.currentModuleIndex].ecopartnerList.forEach((item) => {
            if (item.name === data.companyId) {
              let com = {
                ecopartnerName: value.company,
                contactPhone: value.phone,
                contactName: value.contact,
                userId: value.userId,
                contactList: data.contactList,
                enterpriseId: value.enterpriseId,
                sync: value.sync,
                auth: value.auth,
                approve: value.approve,
                totalScore: value.totalScore,
                introScore: value.introScore,
              };
              item.company.unshift(com);
            }
          });
        }
        message.success("添加成功");
        data.formData.moduleForm[data.currentModuleIndex].companyIdList = data.formData.moduleForm[data.currentModuleIndex].ecopartnerList[0].company.map((item) => { return item.enterpriseId; });
        data.contactList = [];
        data.showAdd = false;
      } catch (error) { }
    };
    const dealNum = (v) => {
      if (v.dealType !== "2" && v.dealType !== "1") {
        return (v.satisfiedScore = "-");
      } else {
        if (v.satisfiedScore) {
          return (v.satisfiedScore = v.satisfiedScore);
        } else {
          return (v.satisfiedScore = "-");
        }
      }
    };
    const dealNumNew = (v) => {
      if (v.dealType !== "2" && v.dealType !== "1") {
        return (v.responseScore = "-");
      } else {
        if (v.responseScore) {
          return (v.responseScore = v.responseScore);
        } else {
          return (v.responseScore = "-");
        }
      }
    };
    // 生态厂商单选事件
    const onCheckChange = (e, item, index) => {
      const { value } = e.target;
      const { action } = data;
      if (action === "edit" || data.formData.moduleForm[index].status === "toSelect" || data.formData.moduleForm[index].status === "reStar") {
        data.formData.moduleForm[index].selectCompanyList = item;
        data.formData.moduleForm[index].ipartnerId = item.userId;
        data.formData.moduleForm[index].selectId = value;
        data.formData.moduleForm[index].selectIdOwn = value;
        data.formData.moduleForm[index].selectPhone = value;
      } else {
        data.formData.moduleForm[index].selectCompanyList = item;
        data.formData.moduleForm[index].ipartnerId = item.userId;
        data.formData.moduleForm[index].enterpriseId = item.enterpriseId;
        data.formData.moduleForm[index].selectId = value;
        data.formData.moduleForm[index].selectIdOwn = value;
        data.formData.moduleForm[index].selectPhone = value;
      }
    };
    // 未支撑确认确定事件
    const confirmRejectType = async (formData) => {
      data.confirmLoading = true;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: formData.rejectType == 5 ? formData.suggest : "发起人结束调度",
        variables: {
          rejectType: Number(formData.rejectType),
          userId: operateModule.selectCompanyList.userId,
        },
      }];
      cityBack(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
      }).catch(() => {
        data.confirmLoading = false;
      });
    };
    const handleOkSugget = async (formData) => {
      data.confirmLoading = true;
      if (!data.isRejectMode) {
        // 同意反馈
        agreeApply(formData);
      } else {
        // 拒绝反馈
        refuseApply(formData);
      }
    };
    const agreeApply = (formData) => {
      // 合作方处理同意
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: formData.suggest,
        variables: {
          chooseType: "1",
          customDataForm: JSON.stringify({ ...operateModule.textList[0].allParseData, ...formData }),
        },
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        if (res.code == 200) {
          data.suggest = "";
          handlExistTask();
          data.showSupportFeedbackModal = false;
        }
      }).finally(() => {
        data.addLoading = false;
        data.confirmLoading = false;
      });
    }
    const showApproveModal = () => {
      data.showSupportFeedbackModal = true;
      data.isRejectMode = false;
      data.isThirdEco = data.userInfo.roleKeyList.includes('ecologicalPartner') ? true : false; // 是否生态厂商
      if (data.isThirdEco) {
        // 生态厂商不需要填写支撑人/天数
        data.dealRules.supportCos = [
          {
            required: false,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ];
      } else {
        // 自有能力方、方案/能力联系人需要填写支撑人/天数
        data.dealRules.supportCos = [
          {
            required: true,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ];
      }
    };
    const showRejectModal = () => {
      data.showSupportFeedbackModal = true;
      data.isRejectMode = true;
    };
    const controlPro = (value, index) => {
      data.formData.moduleForm[index].provinceUser = null;
      data.provincePersonList = []
      if (value) {
        let roleKey = "provinceIndustryCharge";
        getPerson(roleKey).then((res) => {
          data.provincePersonList = res.data;
        });
      }
    };
    const formatContent = (content) => {
      if (!content) return "";
      content = content.replace(/级/g, "");
      return content;
    };
    const formatRealName = (realName, remark) => {
      if (!realName) return "";
      realName = realName.replace(/级/g, "");
      if (realName.includes("王达伟")) {
        return "王达伟(省公司交通、融合创新行业支撑负责人)";
      }
      if (realName.includes("仲伟奇")) {
        return "仲伟奇(省公司农业文旅行业支撑负责人)";
      }
      if (realName.includes("孙晓星")) {
        return "孙晓星(省公司政企行业支撑负责人)";
      }
      if (realName.includes("魏宇珺")) {
        return "魏宇珺(省公司教育行业支撑负责人)";
      }
      if (realName.includes("丁德胜")) {
        return "丁德胜(省公司医疗行业支撑负责人)";
      }
      if (realName.includes("徐剑宏")) {
        return "徐剑宏(省公司政法公安行业支撑负责人)";
      }
      if (realName.includes("肖明")) {
        return "肖明(省公司党政行业支撑负责人)";
      }
      if (realName.includes("许文杰")) {
        return "许文杰(省公司金融、互联网行业支撑负责人)";
      }
      if (realName.includes("吴鹏")) {
        return "吴鹏(省公司工业行业支撑负责人)";
      }
      if (realName.includes("戴云平")) {
        return "戴云平(省直项目交付团队负责人)";
      }
      if (remark && remark.trim() !== "") {
        return `${realName}(${remark.trim()})`;
      }

      return realName;
    };
    const checkSupportTime = (timeStr) => {
      // 处理可能的年月日格式
      const normalizedTime = timeStr.replace(/[年月]/g, '-').replace('日', '');
      const supportTime = dayjs(normalizedTime);
      const currentDate = dayjs().startOf('day');
      const minSupportTime = currentDate.add(3, 'day');

      return supportTime.isAfter(minSupportTime) || supportTime.isSame(minSupportTime);
    };
    const disabledDate = (current) => {
      const today = dayjs().startOf("day");
      return current && current < today.add(3, 'days');
    };
    // 确认修改支撑时限
    const confirmModifySupportTime = (value) => {
      modifySupportTimeFormRef.value?.validate().then(() => {
        const newTime = dayjs(value.supportTime).format('YYYY-MM-DD');
        // 更新支撑时限
        if (data.currentOperateModule) {
          data.currentOperateModule.time = newTime;
        }
        // 继续执行调度操作
        if (data.currentOperationStatus === 'toSelect') {
          if (data.contralSupport) {
            handleProvincialSupport(data.currentOperateModule);
          } else {
            handleCityAndProvince(data.currentOperateModule);
          }
        } else if (data.currentOperationStatus === 'reSelectPage') {
          handleReSelectPage(data.currentOperateModule);
        } else if (data.currentOperationStatus === 'reSubmit') {
          // 重新派单、重新发起
          doSubmit();
        }
        data.showModifySupportTimeModal = false;
      }).catch(() => { });
    };
    // 获取新增生态厂商时的可选项数据
    const fetchData = async () => {
      fetching.value = true;
      try {
        const response = await selectTree();
        const mockData = response.data.filter((item) => {
          return item.id != "1189";
        });
        data.teamOldList = mockData || [];
        displayOptions.value = mockData;
      } catch (error) {
        console.error("Failed to fetch options:", error);
        // 可以在这里设置错误状态或显示错误消息
        displayOptions.value = [];
      } finally {
        fetching.value = false;
      }
    };
    onMounted(fetchData);
    const setFileData = (fileInfo, moduleItem) => {
      moduleItem.fileList = fileInfo.fileList;
    };
    const setFileDataDeal = (fileInfo) => {
      data.formDataDeal.fileListDeal = fileInfo.fileList;
    };
    const viewFileData = (view) => {
      data.viewLoading = view;
    };
    const download = (file) => {
      const href = file.url || file.fileUrl;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
      return false;
    };
    const view = (file) => {
      data.viewLoading = true;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      pptTopdf({
        filePath: file.path,
      }).then((res) => {
        if (res.code == 200) {
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1];
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: file.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      }).finally(() => {
        data.viewLoading = false;
      });
      return false;
    };
    const handleEcopartnerInfo = async (phone) => {
      try {
        const val = await getAllUserList({ phone, scope: 0 });
        if (!val.data?.[0]) return;
        const orgInfo = val.data[0].orgAllName?.split(",") || [];
        if (orgInfo.length === 2 && orgInfo[1] === "苏移集成") {
          return "江苏移动信息系统集成有限公司";
        }
        return orgInfo[1] || val.data[0].orgName;
      } catch (error) {
        console.error("获取生态能力方信息失败:", error);
        return null;
      }
    };
    // 评分完成、点结束需要转售中和调度人自己支撑，是否转售中的弹窗确定事件
    const handleOkScore = async (value) => {
      const operateModule = data.formData.moduleForm[data.operateIndex];
      if (operateModule.status === 'writeScore') {
        // 先评分
        const operateModule = data.formData.moduleForm[data.operateIndex];
        // 只提交同意的且没有评过分的生态方数据
        const ownPerson = operateModule.textList.filter((item) => !item.enterpriseId && (!item.scored && (item.dealType == '1' || item.dealType == '3'))); // enterpriseId为null是方案/能力联系人或自有能力方，不需要打分，需要生态评价
        const thirdPerson = operateModule.textList.filter((item) => item.enterpriseId && (!item.scored && (item.dealType == '1' || item.dealType == '3'))); // enterpriseId非null是生态厂商，需要打分，需要生态评价
        // 移除allParseData
        const filteredOwnPerson = ownPerson.map(({ allParseData, ...rest }) => rest);
        const filteredThirdPerson = thirdPerson.map(({ allParseData, ...rest }) => rest);
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "评分完毕",
          variables: {
            addScoreInfo: JSON.stringify({ writeScore: ownPerson.length > 0 ? filteredOwnPerson : filteredThirdPerson }),
          },
        }];
        try {
          // 先进行评分
          await batchSubmitWithdrawProcess(postData);
          // 再转售中
          const postData2 = {
            orderId: Route.query.orderId,
            projectCode: value.projectCode,
            projectName: value.projectName,
            deliveryManager: value.deliveryManager,
            sendMsg: value.isTurn == "1" ? true : false,
          }
          await midDispatch(postData2);
          message.success("操作成功");
          handlExistTask();
        } catch (error) {
          console.error("操作失败:", error);
          message.error("操作失败");
          data.addLoading = false;
        }
      } else {
        // 调度人自己支撑结束流程时（ submitPage ），转售中
        // 发起人结束工单需要转售中时（ reStar,timeoutReject ），转售中
        const operateModule = data.formData.moduleForm[data.operateIndex];
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "发起人结束调度",
          variables: {
            suggest: "", // 生态评价
            sendMsg: true, //是否发短信
          },
        }];
        cityBack(postData).then(async (res) => {
          const postData2 = {
            orderId: Route.query.orderId,
            projectCode: value.projectCode,
            projectName: value.projectName,
            deliveryManager: value.deliveryManager,
            sendMsg: value.isTurn == "1" ? true : false,
          };
          await midDispatch(postData2);
          message.success("操作成功");
          handlExistTask();
        });
      }
    };
    const closeRejectReasonModal = () => {
      data.showRejectReasonModal = false;
    };
    const filterOption = (input, option) => {
      return option.label.indexOf(input) >= 0;
    };
    const toCompanyDetail = (item) => {
      let phone = JSON.parse(localStorage.getItem("userInfo")).phone;
      if (item.ecopartnerId > 10000) {
        window.open(
          // "http://***********:8013/static/detail?partner_id=" +
          "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.ecopartnerId + "&token=bhubh3333ugy&phone=" + phone, "_blank"
        );
      } else {
        window.open(
          // "http://***********:8013/static/detail?partner_id=" +
          "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.enterpriseId + "&token=bhubh3333ugy&phone=" + phone, "_blank"
        );
      }
    };
    const showModuleTypeDialog = (index) => {
      data.moduleTypeVisible = true;
      data.currentModuleIndex = index;
    };
    // 模块选择弹窗确定事件
    const handleSelectModule = (moduleType) => {
      const index = data.currentModuleIndex;
      const existModule = data.formData.moduleForm.some(item =>
        item.name == moduleType.name && item.uid == moduleType.id
      );
      if (existModule) {
        message.warning("该模块已存在");
        return;
      }
      if (typeof index === 'number' && data.formData.moduleForm[index]) {
        data.formData.moduleForm[index].uid = moduleType.id;
        data.formData.moduleForm[index].name = moduleType.name;
        data.formData.moduleForm[index].intro = moduleType.intro || moduleType.description || moduleType.summary || moduleType.abilityIntro || ""; '';
        formatEcopartnerList(index, moduleType);
      }
      data.moduleTypeVisible = false;
      formatOrderTitle();
    };
    const formatOrderTitle = () => {
      const exitMainModule = data.formData.moduleForm.find((item) => {
        return item.contentType === '1' && item.name;
      });
      if (exitMainModule) {
        // 有方案，则工单标题以方案开头
        data.formData.title = "关于" + exitMainModule.name + "的售前支撑工单";
      } else {
        for (let i = 0; i < data.formData.moduleForm.length; i++) {
          if (data.formData.moduleForm[i].name) {
            // 没有方案，则工单标题以第一个支撑模块开头
            data.formData.title = "关于" + data.formData.moduleForm[i].name + "的售前支撑工单";
            break;
          }
        }
      }
    };
    const changeBusinessModuleType = (val, index) => {
      const module = data.formData.moduleForm[index];
      if (module) {
        module.uid = null;
        module.contentType = val.target.value;
        module.name = '';
        module.intro = '';
        module.ecologyType = '';
        module.selectUsers = [];
        if (module.ecopartnerList?.[0]) {
          const firstItem = module.ecopartnerList[0];
          firstItem.company = [];
          firstItem.ownPerson = [];
          firstItem.ownProvince = [];
        }
      }
    };
    // 删除支撑模块
    const handleDeleteModule = (index) => {
      if (data.formData.moduleForm.length > 1) {
        data.formData.moduleForm.splice(index, 1);
      }
    };
    // 新增支撑模块
    const addModule = () => {
      moduleFormUtils.addModuleForms();
    };
    const limitLength = (e) => {
      let value = e.target.value;
      // 项目编码和商机编码可以输入英文和数字
      value = value.replace(/[^a-zA-Z0-9]/g, '');
      if (value.length > 30) {
        value = value.slice(0, 30);
      }
      data.formData.projectCode = value;
    };
    const actionIsSupport = (item) => {
      if (!item) return ""
      if (item.dealType == '1') {
        return '同意';
      } else if (item.dealType == '2') {
        return '拒绝';
      } else if (item.dealType == '3') {
        return "超时拒绝";
      }
      return '';
    };
    const onSupportChange = (e, index) => {
      const { value } = e.target;
      if (value == "1") {
        data.formData.moduleForm[index].isReassign = "";
      } else if (value == "0") {
        data.formData.moduleForm[index].isReassign = "0";
      }
    };
    const reSubmit = async (index) => {
      await mainFormRef.value?.validate().then(() => {
        try {
          data.addLoading = true;
          const operateModule = data.formData.moduleForm[index];
          // 检查能力方是否已拒绝
          const isRejected = operateModule.rejectCompanyIdlist.some(
            value => value.userId === operateModule.selectCompanyList.userId
          );
          if (isRejected) {
            const msg = operateModule.selectCompanyList.enterpriseId ? "该厂商联系人已拒绝，请重新选择" : "该自有能力方已拒绝，请重新选择";
            message.warning(msg);
            data.addLoading = false;
            return;
          }
          if (!operateModule.dispatchUser) {
            message.warning("请选择调度管理员");
            data.addLoading = false;
            return
          }
          data.currentOperateModule = operateModule;
          if (!checkSupportTime(operateModule.time)) {
            data.modifySupportTimeForm.originalSupportTime = operateModule.time;
            data.currentOperationStatus = 'reSubmit'; // 保存操作状态
            data.showModifySupportTimeModal = true;
            data.addLoading = false;
            return;
          }
          doSubmit();
        } catch (error) {
          console.log(error);
          data.addLoading = false;
        }
      }).catch(() => { });
    };
    const confirmUnSupport = (index) => {
      data.operateIndex = index;
      data.showRejectTypeModal = true; // 显示拒绝支撑类型弹窗
    }
    const endWork = (index) => {
      data.operateIndex = index;
      data.endWorkLoading = true; // 开始loading
      const operateModule = data.formData.moduleForm[data.operateIndex];
      if (operateModule.status == "reStar" || operateModule.status == "timeoutReject") {
        // 调度人驳回、生态方反馈超时，发起人点击结束
        // 查看是工单是否可以转售中
        querytOrderById(Route.query.orderId).then((res) => {
          const scored = res.data.scored;
          const taskList = res.data.todoTasks || [];
          const procCount = res.data.procCount;
          if (procCount == 1) {
            // 单模块结束
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "发起人结束调度",
            }];
            cityBack(postData).then((res) => {
              message.success(res.msg);
              handlExistTask();
            }).catch(() => {
              data.endWorkLoading = false;
            });
            return
          }
          if (taskList.length > 1) {
            // 不是最后一个结束的模块，不弹售中弹窗
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "发起人结束调度",
            }];
            cityBack(postData).then((res) => {
              message.success(res.msg);
              handlExistTask();
            }).catch(() => {
              data.endWorkLoading = false;
            });
          } else {
            if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId && scored) {
              // 最后一个结束的模块且taskList中的taskId等于当前的taskId，弹售中弹窗
              data.formDataScore.projectCode = data.formData.projectCode;
              data.formDataScore.projectName = data.formData.projectName;
              data.formDataScore.deliveryManager = undefined;
              data.formDataScore.isTurn = "2";
              data.showPostScoreModal = true;
              data.endWorkLoading = false;
            } else {
              // 不弹售中弹窗
              const postData = [{
                taskId: operateModule.taskId,
                procInsId: operateModule.procInstId,
                comment: "发起人结束调度",
              }];
              cityBack(postData).then((res) => {
                message.success(res.msg);
                handlExistTask();
              }).catch(() => {
                data.endWorkLoading = false;
              });
            }
          }
        }).catch(() => {
          data.endWorkLoading = false;
        });
      } else {
        // 调度人自己支撑结束流程时（ submitPage ），转售中
        querytOrderById(Route.query.orderId).then(async (res) => {
          const scored = res.data.scored;
          const taskList = res.data.todoTasks || [];
          const procCount = res.data.procCount;
          if (procCount == 1) {
            // 单模块,弹转售中
            data.formDataScore.projectCode = data.formData.projectCode;
            data.formDataScore.projectName = data.formData.projectName;
            data.formDataScore.deliveryManager = undefined;
            data.formDataScore.isTurn = "2";
            data.showPostScoreModal = true;
            data.endWorkLoading = false;
            return
          }
          if (taskList.length > 1) {
            // 不是最后一个结束的模块，不弹售中弹窗
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "发起人结束调度",
            }];
            cityBack(postData).then((res) => {
              message.success(res.msg);
              handlExistTask();
            }).catch(() => {
              data.endWorkLoading = false;
            });
          } else {
            if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId && scored) {
              // 最后一个结束的模块且taskList中的taskId等于当前的taskId，弹售中弹窗
              data.formDataScore.projectCode = data.formData.projectCode;
              data.formDataScore.projectName = data.formData.projectName;
              data.formDataScore.deliveryManager = undefined;
              data.formDataScore.isTurn = "2";
              data.showPostScoreModal = true;
              data.endWorkLoading = false;
            } else {
              // 不弹售中弹窗
              const postData = [{
                taskId: operateModule.taskId,
                procInsId: operateModule.procInstId,
                comment: "发起人结束调度",
              }];
              cityBack(postData).then((res) => {
                message.success(res.msg);
                handlExistTask();
              }).catch(() => {
                data.endWorkLoading = false;
              });
            }
          }
        }).catch(() => {
          data.endWorkLoading = false;
        });
      };
    };
    const formatTime = (time) => {
      if (!time) return "-";
      const normalizedTime = time.replace(/[年月]/g, '-').replace('日', '');
      return dayjs(normalizedTime).format('YYYY年MM月DD日');
    };
    const confirmSupported = () => {
      if (!data.currentOperateModule) return;

      data.addLoading = true;
      const operateModule = data.currentOperateModule;

      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: "选择已支撑",
        variables: {
          hasSupported: true,
        },
      }];

      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
      }).finally(() => {
        data.addLoading = false;
        data.showConfirmSupportedModal = false;
        data.currentOperateModule = null;
      });
    };
    const cancelSupported = () => {
      data.showConfirmSupportedModal = false;
      data.currentOperateModule = null;
    };
    const doSubmit = () => {
      data.addLoading = true;
      // 更新模块时间
      data.currentOperateModule.projectName = data.formData.projectName;
      data.currentOperateModule.projectCode = data.formData.projectCode;
      data.currentOperateModule.userPhone = data.formData.userPhone;
      data.currentOperateModule.userInfo = data.formData.userInfo;
      data.currentOperateModule.supportMehod = data.formData.supportMehod;
      const { ecopartnerList, ...moduleFormWithoutEcopartner } = data.currentOperateModule;
      moduleFormWithoutEcopartner.company = ecopartnerList?.[0]?.company || []; // 生态厂商list
      moduleFormWithoutEcopartner.ownPerson = ecopartnerList?.[0]?.ownPerson || []; // 自有能力方list
      const postData = [{
        taskId: data.currentOperateModule.taskId,
        procInsId: data.currentOperateModule.procInstId,
        comment: "重新提交",
        nextUserIds: data.currentOperateModule.dispatchUser,
        variables: {
          customDataForm: JSON.stringify(moduleFormWithoutEcopartner),
          nextUserIds: data.currentOperateModule.dispatchUser,
          isProvince: data.isProvinceUser,
          supportTime: data.currentOperateModule.time,
          timeout: false,
        },
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success("提交成功");
        handlExistTask();
      }).catch(() => {
        data.addLoading = false;
      });
    };
    const formatScore = (compantItem) => {
      if (compantItem.totalScore) {
        return compantItem.totalScore;
      } else if (compantItem.introScore) {
        return compantItem.introScore;
      } else {
        return "-";
      }
    }
    // 执行搜索逻辑
    const executeSearch = (ecopartnerName, list) => {
      let filteredList = [...list];
      // 按企业或联系人名称过滤（支持模糊搜索）
      if (ecopartnerName && ecopartnerName.trim()) {
        filteredList = filteredList.filter(company =>
        (company.ecopartnerName &&
          company.ecopartnerName.includes(ecopartnerName.trim()) || (company.contactName && company.contactName.includes(ecopartnerName.trim())))
        );
      }
      return filteredList;
    };
    const handleSearchCompany = (module) => {
      // 标记搜索已应用
      module.searchState.isApplied = true;
      // 清除缓存，强制重新计算
      module.searchState.cachedResults = null;
    };
    const filterCompanyList = (module, allList) => {
      // 如果没有应用搜索，直接返回原始数据的结果
      if (!module.searchState.isApplied) {
        return allList;
      }
      // 检查是否有缓存结果
      if (module.searchState.cachedResults) {
        return module.searchState.cachedResults;
      }
      // 执行搜索并缓存结果
      const filteredResults = executeSearch(module.searchForm.ecopartnerName, allList);
      module.searchState.cachedResults = filteredResults;
      // 返回分页结果
      return filteredResults;
    };
    const moduleExistInQuery = (uid) => {
      return data.queryModuleIdList.some(item => item.ids && item.ids.split(',').includes(String(uid)));
    };
    const fromOneKeyDispatch = () => {
      return Route.query.from === 'oneKeyDispatch';
    };
    const getPopupContainer = () => {
      return triggerNode => triggerNode.parentNode;
    }
    // 处理支撑信息组件的评分变化事件
    const handleScoreChange = ({ type, value, supportInfo }) => {
      if (type === 'satisfiedScore') {
        supportInfo.satisfiedScore = value;
      } else if (type === 'responseScore') {
        supportInfo.responseScore = value;
      }
    };
    // 处理支撑信息组件的评价变化事件
    const handleEvaluationChange = ({ value, supportInfo }) => {
      supportInfo.suggest = value;
    };
    const handleFormDataUpdate = (newFormData) => {
      console.log(111,newFormData)
      Object.assign(data.formData, newFormData);
    };
    // const handleProjectCodeTypeChange = () => {
    //   data.formData.projectName = undefined;
    //   data.formData.projectCode = undefined;
    // };
    // 处理项目名称变化时同步项目编码
    // const handleProjectNameChange = (value) => {
    //   // 根据选中的项目名称查找对应的项目编码
    //   const selectedItem = data.projectOptions.find(item => item.name === value);
    //   if (selectedItem) {
    //     data.formData.projectCode = selectedItem.code;
    //   } else {
    //     data.formData.projectCode = undefined;
    //   }
    // };
    // 处理项目编码变化时同步项目名称
    // const handleProjectCodeChange = (value) => {
    //   // 根据选中的项目编码查找对应的项目名称
    //   const selectedItem = data.projectOptions.find(item => item.code === value);
    //   if (selectedItem) {
    //     data.formData.projectName = selectedItem.name;
    //   } else {
    //     data.formData.projectName = undefined;
    //   }
    // };
    return {
      ...toRefs(data),
      mainFormRef,
      Router,
      Route,
      selectedValues,
      displayOptions,
      fetching,
      modifySupportTimeFormRef,
      businessModuleTypeComputed,
      changeBusinessModuleType,
      reSubmit,
      endWork,
      confirmUnSupport,
      handleDeleteModule,
      handleSelectModule,
      showModuleTypeDialog,
      toCompanyDetail,
      filterOption,
      backLastComit,
      closeRejectReasonModal,
      handleOkScore,
      download,
      view,
      setFileData,
      setFileDataDeal,
      viewFileData,
      disabledDate,
      dealNumNew,
      controlPro,
      confirmRejectType,
      handleOkSugget,
      showApproveModal,
      showRejectModal,
      backout,
      submitAdd,
      selectUserCom,
      handleReject,
      dealNum,
      onCheckChange,
      ecologyChangeOld,
      // handleSearch,
      addCooperate,
      handleModuleSubmit,
      createOrder,
      formatRealName,
      formatContent,
      addModule,
      limitLength,
      actionIsSupport,
      onSupportChange,
      confirmModifySupportTime,
      toChinese,
      formatTime,
      confirmSupported,
      cancelSupported,
      formatScore,
      handleSearchCompany,
      filterCompanyList,
      moduleExistInQuery,
      fromOneKeyDispatch,
      getPopupContainer,
      handleScoreChange,
      handleEvaluationChange,
      handleFormDataUpdate,
      // handleProjectCodeTypeChange,
      // handleProjectNameChange,
      // handleProjectCodeChange,
      getProcess,
    };
  },
});
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
<style lang="scss">
.resize-table-header-line.el-table {

  // 默认表头和单元格使用默认光标
  th.el-table__cell,
  td>.cell {
    cursor: default !important;
  }

  // 只在表头分隔线位置显示调整列宽光标
  th.el-table__cell {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px; // 分隔线热区宽度
      height: 100%;
      cursor: default;
      transform: translateX(50%); // 居中显示
    }

    &:hover::after {
      cursor: col-resize !important;
    }
  }
}
</style>